2025-07-30 17:16:59 | SUCCESS | 读取主设置成功
2025-07-30 17:16:59 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-30 17:17:00 | INFO | 2025/07/30 17:17:00 GetRedisAddr: 127.0.0.1:6379
2025-07-30 17:17:00 | INFO | 2025/07/30 17:17:00 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-30 17:17:00 | INFO | 2025/07/30 17:17:00 Server start at :9000
2025-07-30 17:17:00 | SUCCESS | WechatAPI服务已启动
2025-07-30 17:17:01 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-30 17:17:01 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-30 17:17:01 | SUCCESS | 登录成功
2025-07-30 17:17:01 | SUCCESS | 已开启自动心跳
2025-07-30 17:17:01 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 17:17:01 | SUCCESS | 数据库初始化成功
2025-07-30 17:17:01 | SUCCESS | 定时任务已启动
2025-07-30 17:17:01 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-30 17:17:01 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:17:02 | INFO | 播客API初始化成功
2025-07-30 17:17:02 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 17:17:02 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-30 17:17:02 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-30 17:17:02 | INFO | [ChatSummary] 数据库初始化成功
2025-07-30 17:17:03 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-30 17:17:03 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-30 17:17:03 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-30 17:17:03 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-30 17:17:03 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-30 17:17:03 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-30 17:17:03 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-30 17:17:03 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:17:03 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-30 17:17:03 | INFO | [RenameReminder] 开始启用插件...
2025-07-30 17:17:03 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-30 17:17:03 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-30 17:17:03 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-30 17:17:03 | INFO | 已设置检查间隔为 3600 秒
2025-07-30 17:17:03 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-30 17:17:04 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-30 17:17:04 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-30 17:17:08 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-30 17:17:08 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-30 17:17:10 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-30 17:17:10 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:17:10 | INFO | [yuanbao] 插件初始化完成
2025-07-30 17:17:10 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-30 17:17:10 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-30 17:17:10 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-30 17:17:10 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-30 17:17:10 | INFO | 处理堆积消息中
2025-07-30 17:17:10 | DEBUG | 接受到 85 条消息
2025-07-30 17:17:11 | SUCCESS | 处理堆积消息完毕
2025-07-30 17:17:11 | SUCCESS | 开始处理消息
2025-07-30 17:21:51 | DEBUG | 收到消息: {'MsgId': 365515673, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753867311, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_P1NyiWgt|v1_BYHIJEek</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女', 'NewMsgId': 6286194258400802651, 'MsgSeq': 871412204}
2025-07-30 17:21:51 | INFO | 收到文本消息: 消息ID:365515673 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女
2025-07-30 17:21:51 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女 from wxid_ubbh6q832tcs21
2025-07-30 17:21:51 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女
2025-07-30 17:21:52 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 17:21:52 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: text
2025-07-30 17:21:52 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:搞不出来
2025-07-30 17:21:52 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 0.65秒
2025-07-30 17:21:52 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 17:21:52 | DEBUG | 处理消息内容: '找视频 美女'
2025-07-30 17:21:52 | DEBUG | 消息内容 '找视频 美女' 不匹配任何命令，忽略
2025-07-30 17:27:07 | DEBUG | 收到消息: {'MsgId': 749042192, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="6a7bb44811f5639fb0c768c70fe298c0" len="479668" productid="com.tencent.xin.emoticon.person.stiker_17370056031693df064a067e6d" androidmd5="6a7bb44811f5639fb0c768c70fe298c0" androidlen="479668" s60v3md5="6a7bb44811f5639fb0c768c70fe298c0" s60v3len="479668" s60v5md5="6a7bb44811f5639fb0c768c70fe298c0" s60v5len="479668" cdnurl="http://wxapp.tc.qq.com/275/20304/stodownload?m=6a7bb44811f5639fb0c768c70fe298c0&amp;filekey=30350201010421301f020201130402534804106a7bb44811f5639fb0c768c70fe298c002030751b4040d00000004627466730000000132&amp;hy=SH&amp;storeid=267888267000a05bfb44b0f2a0000011300004f50534813d8b17156aa3a45a&amp;bizid=1023" designerid="" thumburl="http://wxapp.tc.qq.com/275/20304/stodownload?m=8b25f296f2ff36e7f2956bf4ca18ddbd&amp;filekey=30350201010421301f020201130402534804108b25f296f2ff36e7f2956bf4ca18ddbd0203008132040d00000004627466730000000132&amp;hy=SH&amp;storeid=26788833b000d3e76b44b0f2a0000011300004f50534829ee217156a5713c7&amp;bizid=1023" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=9b8a1143b18426be92eee6c610ab622f&amp;filekey=30350201010421301f020201060402534804109b8a1143b18426be92eee6c610ab622f02030751c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2678e232c000ebb2eb44b0f2a0000010600004f5053482c87c0d1567cc8dca&amp;bizid=1023" aeskey="368e02c006174da8bf0f01c9ede2e919" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=dcbd0132143cd132db269ead5d2ac2b6&amp;filekey=30350201010421301f020201060402535a0410dcbd0132143cd132db269ead5d2ac2b602030161a0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2678e2496000992b50801acb20000010600004f50535a0affb151567b9dcc3&amp;bizid=1023" externmd5="0132587bb8a40e32f95960c58a218fc9" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="ChEKB2RlZmF1bHQSBumch+aDigoRCgdkZWZhdWx0EgbpnIfmg4o="></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753867626, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_qEUTIP7o|v1_7gaMARPo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 478424117891422164, 'MsgSeq': 871412207}
2025-07-30 17:27:07 | INFO | 收到表情消息: 消息ID:749042192 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:6a7bb44811f5639fb0c768c70fe298c0 大小:479668
2025-07-30 17:27:07 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 478424117891422164
