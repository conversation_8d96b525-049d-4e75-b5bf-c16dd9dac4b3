2025-07-30 17:16:59 | SUCCESS | 读取主设置成功
2025-07-30 17:16:59 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-30 17:17:00 | INFO | 2025/07/30 17:17:00 GetRedisAddr: 127.0.0.1:6379
2025-07-30 17:17:00 | INFO | 2025/07/30 17:17:00 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-30 17:17:00 | INFO | 2025/07/30 17:17:00 Server start at :9000
2025-07-30 17:17:00 | SUCCESS | WechatAPI服务已启动
2025-07-30 17:17:01 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-30 17:17:01 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-30 17:17:01 | SUCCESS | 登录成功
2025-07-30 17:17:01 | SUCCESS | 已开启自动心跳
2025-07-30 17:17:01 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 17:17:01 | SUCCESS | 数据库初始化成功
2025-07-30 17:17:01 | SUCCESS | 定时任务已启动
2025-07-30 17:17:01 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-30 17:17:01 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:17:02 | INFO | 播客API初始化成功
2025-07-30 17:17:02 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['***********@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '***********@chatroom']}}
2025-07-30 17:17:02 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['***********@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '***********@chatroom']}}
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-30 17:17:02 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-30 17:17:02 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-30 17:17:02 | INFO | [ChatSummary] 数据库初始化成功
2025-07-30 17:17:03 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-30 17:17:03 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-30 17:17:03 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-30 17:17:03 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-30 17:17:03 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-30 17:17:03 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-30 17:17:03 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-30 17:17:03 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:17:03 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-30 17:17:03 | INFO | [RenameReminder] 开始启用插件...
2025-07-30 17:17:03 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-30 17:17:03 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-30 17:17:03 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-30 17:17:03 | INFO | 已设置检查间隔为 3600 秒
2025-07-30 17:17:03 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-30 17:17:04 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-30 17:17:04 | DEBUG | 已更新群 ***********@chatroom 的成员列表
2025-07-30 17:17:08 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-30 17:17:08 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-30 17:17:10 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-30 17:17:10 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:17:10 | INFO | [yuanbao] 插件初始化完成
2025-07-30 17:17:10 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-30 17:17:10 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-30 17:17:10 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-30 17:17:10 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-30 17:17:10 | INFO | 处理堆积消息中
2025-07-30 17:17:10 | DEBUG | 接受到 85 条消息
2025-07-30 17:17:11 | SUCCESS | 处理堆积消息完毕
2025-07-30 17:17:11 | SUCCESS | 开始处理消息
2025-07-30 17:21:51 | DEBUG | 收到消息: {'MsgId': 365515673, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753867311, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_P1NyiWgt|v1_BYHIJEek</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女', 'NewMsgId': 6286194258400802651, 'MsgSeq': 871412204}
2025-07-30 17:21:51 | INFO | 收到文本消息: 消息ID:365515673 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女
2025-07-30 17:21:51 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女 from wxid_ubbh6q832tcs21
2025-07-30 17:21:51 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女
2025-07-30 17:21:52 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 17:21:52 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: text
2025-07-30 17:21:52 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:搞不出来
2025-07-30 17:21:52 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 0.65秒
2025-07-30 17:21:52 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 17:21:52 | DEBUG | 处理消息内容: '找视频 美女'
2025-07-30 17:21:52 | DEBUG | 消息内容 '找视频 美女' 不匹配任何命令，忽略
2025-07-30 17:27:07 | DEBUG | 收到消息: {'MsgId': 749042192, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="6a7bb44811f5639fb0c768c70fe298c0" len="479668" productid="com.tencent.xin.emoticon.person.stiker_17370056031693df064a067e6d" androidmd5="6a7bb44811f5639fb0c768c70fe298c0" androidlen="479668" s60v3md5="6a7bb44811f5639fb0c768c70fe298c0" s60v3len="479668" s60v5md5="6a7bb44811f5639fb0c768c70fe298c0" s60v5len="479668" cdnurl="http://wxapp.tc.qq.com/275/20304/stodownload?m=6a7bb44811f5639fb0c768c70fe298c0&amp;filekey=30350201010421301f020201130402534804106a7bb44811f5639fb0c768c70fe298c002030751b4040d00000004627466730000000132&amp;hy=SH&amp;storeid=267888267000a05bfb44b0f2a0000011300004f50534813d8b17156aa3a45a&amp;bizid=1023" designerid="" thumburl="http://wxapp.tc.qq.com/275/20304/stodownload?m=8b25f296f2ff36e7f2956bf4ca18ddbd&amp;filekey=30350201010421301f020201130402534804108b25f296f2ff36e7f2956bf4ca18ddbd0203008132040d00000004627466730000000132&amp;hy=SH&amp;storeid=26788833b000d3e76b44b0f2a0000011300004f50534829ee217156a5713c7&amp;bizid=1023" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=9b8a1143b18426be92eee6c610ab622f&amp;filekey=30350201010421301f020201060402534804109b8a1143b18426be92eee6c610ab622f02030751c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2678e232c000ebb2eb44b0f2a0000010600004f5053482c87c0d1567cc8dca&amp;bizid=1023" aeskey="368e02c006174da8bf0f01c9ede2e919" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=dcbd0132143cd132db269ead5d2ac2b6&amp;filekey=30350201010421301f020201060402535a0410dcbd0132143cd132db269ead5d2ac2b602030161a0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2678e2496000992b50801acb20000010600004f50535a0affb151567b9dcc3&amp;bizid=1023" externmd5="0132587bb8a40e32f95960c58a218fc9" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="ChEKB2RlZmF1bHQSBumch+aDigoRCgdkZWZhdWx0EgbpnIfmg4o="></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753867626, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_qEUTIP7o|v1_7gaMARPo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 478424117891422164, 'MsgSeq': 871412207}
2025-07-30 17:27:07 | INFO | 收到表情消息: 消息ID:749042192 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:6a7bb44811f5639fb0c768c70fe298c0 大小:479668
2025-07-30 17:27:07 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 478424117891422164
2025-07-30 17:34:45 | DEBUG | 收到消息: {'MsgId': 1753867860, 'FromUserName': {'string': 'newsapp'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025073008</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1753867808</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzj+v6fEBkCgzKfEBkjUzKfEBlAB</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868083, 'NewMsgId': 1753867860, 'MsgSeq': 0}
2025-07-30 17:34:45 | DEBUG | 系统消息类型: functionmsg
2025-07-30 17:34:45 | INFO | 未知的系统消息类型: {'MsgId': 1753867860, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025073008</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1753867808</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzj+v6fEBkCgzKfEBkjUzKfEBlAB</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868083, 'NewMsgId': 1753867860, 'MsgSeq': 0, 'FromWxid': 'newsapp', 'SenderWxid': 'newsapp', 'IsGroup': False}
2025-07-30 17:38:54 | DEBUG | 收到消息: {'MsgId': 393488263, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n为啥没推送@帅\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868334, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_ubbh6q832tcs21]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_8t0v0GsT|v1_yn+5P0Am</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8524687453670605244, 'MsgSeq': 871412208}
2025-07-30 17:38:54 | INFO | 收到文本消息: 消息ID:393488263 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:['wxid_ubbh6q832tcs21'] 内容:为啥没推送@帅 
2025-07-30 17:38:54 | DEBUG | 处理消息内容: '为啥没推送@帅'
2025-07-30 17:38:54 | DEBUG | 消息内容 '为啥没推送@帅' 不匹配任何命令，忽略
2025-07-30 17:39:35 | DEBUG | 收到消息: {'MsgId': 883848528, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg>\n\t\t<title>更新公告 | 六周年庆典狂欢开启，邮轮/骗子酒馆/跳跳乐新玩法重磅上线！</title>\n\t\t<sourceusername>gh_7057516c9e71</sourceusername>\n\t\t<appattach>\n\t\t\t<cdnthumblength>5523</cdnthumblength>\n\t\t\t<cdnthumburl>3057020100044b30490201000204062b4e3002032f80290204a4f73db702046889e856042464653036646638612d633839372d343833382d396432302d6538346131643734323335310204052808030201000405004c57c100</cdnthumburl>\n\t\t\t<cdnthumbaeskey>7d587745b7aefd8b9e529b0480734b7e</cdnthumbaeskey>\n\t\t\t<cdnthumbmd5>20609ad46bed0f86044c23bf087aa835</cdnthumbmd5>\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId>3cefced1ba37f1e19fe473f5d8aafd5f_0_1753868373</publisherId>\n\t\t</webviewshared>\n\t\t<type>5</type>\n\t\t<md5>20609ad46bed0f86044c23bf087aa835</md5>\n\t\t<des>重磅升级！六周年狂欢盛典开启，这次小舞带来了超多新玩法！邮轮、骗子酒馆、跳跳乐……准备好了吗，一起来看看本次更新内容吧~</des>\n\t\t<url>https://mp.weixin.qq.com/s?__biz=MzU5NTA5OTMxMA==&amp;mid=2247526759&amp;idx=2&amp;sn=f4cec085ea8544243c9c396f2c4b9c1d&amp;chksm=ff71e13dd222ad86bdbd90f297adfccf521175647765ef6cc227b4f3604b06cad7a2607eda9e&amp;mpshare=1&amp;scene=1&amp;srcid=0730m9lQg2cHFZoEgP4UAgyd&amp;sharer_shareinfo=c0b0ffda4f74cf7a4e7afe5fd90dfc32&amp;sharer_shareinfo_first=c0b0ffda4f74cf7a4e7afe5fd90dfc32#rd</url>\n\t\t<sourcedisplayname>唱舞全明星</sourcedisplayname>\n\t\t<mmreadershare>\n\t\t\t<itemshowtype>0</itemshowtype>\n\t\t</mmreadershare>\n\t</appmsg>\n\t<fromusername>wxid_ikxxrwasicud11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868374, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>50ec9c326e5981a09720a58aa4ef29b8_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_MHBstobF|v1_lqkOsvXS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8118995783855636956, 'MsgSeq': 871412209}
2025-07-30 17:39:35 | DEBUG | 从群聊消息中提取发送者: wxid_ikxxrwasicud11
2025-07-30 17:39:35 | INFO | 收到公众号文章消息: 消息ID:883848528 来自:***********@chatroom
2025-07-30 17:39:35 | ERROR | 解析XML失败: mismatched tag: line 1, column 384
2025-07-30 17:39:35 | DEBUG | [ArticleForwarder] 检测到XML开头被截断，尝试修复
2025-07-30 17:39:35 | DEBUG | [ArticleForwarder] 已修复XML开头截断问题
2025-07-30 17:39:35 | DEBUG | 尝试使用修复后的XML重新解析
2025-07-30 17:39:35 | DEBUG | 从sourcedisplayname提取到公众号: 唱舞全明星
2025-07-30 17:39:35 | DEBUG | 公众号「唱舞全明星」不在监控列表中，跳过处理
2025-07-30 17:42:04 | DEBUG | 收到消息: {'MsgId': 1046802820, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_002lrj9uidgz22:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="d711d04acd12220b01f4ae9c0eb8f5fe" cdnvideourl="3057020100044b3049020100020472948eb302032f51490204a231227502046889e8d9042464363864383430622d666264392d346261642d616639382d3064383762323261393762650204052408040201000405004c543d00" cdnthumbaeskey="d711d04acd12220b01f4ae9c0eb8f5fe" cdnthumburl="3057020100044b3049020100020472948eb302032f51490204a231227502046889e8d9042464363864383430622d666264392d346261642d616639382d3064383762323261393762650204052408040201000405004c543d00" length="8171162" playlength="80" cdnthumblength="20363" cdnthumbwidth="288" cdnthumbheight="512" fromusername="wxid_002lrj9uidgz22" md5="138ca082b98de62efd5bf42304e564d4" newmd5="274b368bec4bd3523b62d052f3958bd3" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868524, 'MsgSource': '<msgsource>\n\t<videopreloadlen>599115</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>1ed07b352f7f94bfdee8712dbf9ea88e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Nrf4Q9Is|v1_dGxylZ1u</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '7在群聊中发了一段视频', 'NewMsgId': 3015657906410266132, 'MsgSeq': 871412210}
2025-07-30 17:42:04 | INFO | 收到视频消息: 消息ID:1046802820 来自:48097389945@chatroom 发送人:wxid_002lrj9uidgz22 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="d711d04acd12220b01f4ae9c0eb8f5fe" cdnvideourl="3057020100044b3049020100020472948eb302032f51490204a231227502046889e8d9042464363864383430622d666264392d346261642d616639382d3064383762323261393762650204052408040201000405004c543d00" cdnthumbaeskey="d711d04acd12220b01f4ae9c0eb8f5fe" cdnthumburl="3057020100044b3049020100020472948eb302032f51490204a231227502046889e8d9042464363864383430622d666264392d346261642d616639382d3064383762323261393762650204052408040201000405004c543d00" length="8171162" playlength="80" cdnthumblength="20363" cdnthumbwidth="288" cdnthumbheight="512" fromusername="wxid_002lrj9uidgz22" md5="138ca082b98de62efd5bf42304e564d4" newmd5="274b368bec4bd3523b62d052f3958bd3" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-07-30 17:43:05 | DEBUG | 收到消息: {'MsgId': 1821140988, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_002lrj9uidgz22:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="e21e2ab3ac20a68f5c5f118ba49ac6ed" cdnvideourl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e924042434393661633964332d323931622d343662352d396530642d30653962313432653162653502040d2408040201000405004c4d9900" cdnthumbaeskey="e21e2ab3ac20a68f5c5f118ba49ac6ed" cdnthumburl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e924042434393661633964332d323931622d343662352d396530642d30653962313432653162653502040d2408040201000405004c4d9900" length="1666607" playlength="22" cdnthumblength="18066" cdnthumbwidth="512" cdnthumbheight="384" fromusername="wxid_002lrj9uidgz22" md5="de7fdd132dd318cb38ee5b20553f904e" newmd5="05921fbe0b3ce41465ef20c0aff75b2f" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868585, 'MsgSource': '<msgsource>\n\t<videopreloadlen>405437</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>f108cda2fe45989925969fe5cf955283_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_3i1Fb64p|v1_2HlFe4Z6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '7在群聊中发了一段视频', 'NewMsgId': 7767348469897650651, 'MsgSeq': 871412211}
2025-07-30 17:43:05 | INFO | 收到视频消息: 消息ID:1821140988 来自:48097389945@chatroom 发送人:wxid_002lrj9uidgz22 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="e21e2ab3ac20a68f5c5f118ba49ac6ed" cdnvideourl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e924042434393661633964332d323931622d343662352d396530642d30653962313432653162653502040d2408040201000405004c4d9900" cdnthumbaeskey="e21e2ab3ac20a68f5c5f118ba49ac6ed" cdnthumburl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e924042434393661633964332d323931622d343662352d396530642d30653962313432653162653502040d2408040201000405004c4d9900" length="1666607" playlength="22" cdnthumblength="18066" cdnthumbwidth="512" cdnthumbheight="384" fromusername="wxid_002lrj9uidgz22" md5="de7fdd132dd318cb38ee5b20553f904e" newmd5="05921fbe0b3ce41465ef20c0aff75b2f" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

