2025-07-30 17:53:51 | SUCCESS | 读取主设置成功
2025-07-30 17:53:51 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-30 17:53:51 | INFO | 2025/07/30 17:53:51 GetRedisAddr: 127.0.0.1:6379
2025-07-30 17:53:51 | INFO | 2025/07/30 17:53:51 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-30 17:53:51 | INFO | 2025/07/30 17:53:51 Server start at :9000
2025-07-30 17:53:52 | SUCCESS | WechatAPI服务已启动
2025-07-30 17:53:52 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-30 17:53:52 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-30 17:53:52 | SUCCESS | 登录成功
2025-07-30 17:53:52 | SUCCESS | 已开启自动心跳
2025-07-30 17:53:52 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 17:53:52 | SUCCESS | 数据库初始化成功
2025-07-30 17:53:52 | SUCCESS | 定时任务已启动
2025-07-30 17:53:52 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-30 17:53:52 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:53:53 | INFO | 播客API初始化成功
2025-07-30 17:53:53 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 17:53:53 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 17:53:53 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-30 17:53:53 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-30 17:53:53 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-30 17:53:53 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-30 17:53:53 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-30 17:53:53 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-30 17:53:53 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-30 17:53:54 | INFO | [ChatSummary] 数据库初始化成功
2025-07-30 17:53:54 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-07-30 17:53:54 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-07-30 17:53:54 | DEBUG |   - 启用状态: True
2025-07-30 17:53:54 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-07-30 17:53:54 | DEBUG |   - 设备ID: 7468716989062841895
2025-07-30 17:53:54 | DEBUG |   - Web ID: 7468716986638386703
2025-07-30 17:53:54 | DEBUG |   - Cookies配置: 已配置
2025-07-30 17:53:54 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-07-30 17:53:54 | DEBUG |   - 自然化响应: True
2025-07-30 17:53:54 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-30 17:53:54 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-30 17:53:54 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-30 17:53:54 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-30 17:53:54 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-30 17:53:54 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-30 17:53:54 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-30 17:53:54 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:53:54 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-30 17:53:54 | INFO | [RenameReminder] 开始启用插件...
2025-07-30 17:53:54 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-30 17:53:54 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-30 17:53:54 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-30 17:53:54 | INFO | 已设置检查间隔为 3600 秒
2025-07-30 17:53:54 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-30 17:53:54 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-30 17:53:55 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-30 17:53:55 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-30 17:53:55 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-30 17:53:55 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-30 17:53:55 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:53:55 | INFO | [yuanbao] 插件初始化完成
2025-07-30 17:53:55 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-30 17:53:55 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-30 17:53:55 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-30 17:53:55 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-30 17:53:55 | INFO | 处理堆积消息中
2025-07-30 17:53:55 | SUCCESS | 处理堆积消息完毕
2025-07-30 17:53:55 | SUCCESS | 开始处理消息
2025-07-30 17:54:04 | DEBUG | 收到消息: {'MsgId': 987982596, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753869244, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_B6YM8m06|v1_zD5bB+Rs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女', 'NewMsgId': 975380943749476215, 'MsgSeq': 871412229}
2025-07-30 17:54:04 | INFO | 收到文本消息: 消息ID:987982596 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女
2025-07-30 17:54:05 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女 from wxid_ubbh6q832tcs21
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 开始处理视频搜索请求，用户: wxid_ubbh6q832tcs21
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 用户限制检查 - 用户: wxid_ubbh6q832tcs21, 上次请求: 1753869245.26秒前, 等待时间: 0.00秒
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 令牌桶状态: 5.00/5
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 成功获取令牌，剩余: 4.00
2025-07-30 17:54:05 | INFO | [DoubaoVideoSearch] 开始搜索视频，关键词: '美女'
2025-07-30 17:54:05 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 调试信息 - 设备ID: 7468716989062841895, Web ID: 7468716986638386703
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 调试信息 - Cookies长度: 2107
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 第1次尝试，开始构造请求参数
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 生成会话ID: 38175386924526428, 消息ID: ba0d6520-95d5-11f0-a188-0d4dde70dcf1
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 构造搜索查询: 搜索抖音视频:美女
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 请求头: {'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Cookie': 's_v_web_id=verify_m6uzxanl_As5qwFjE_PdRC_4O1h_9K4P_StGAfYSOVvJC; passport_csrf_token=03cf1636cd02cb98455735b95a10ee46; passport_csrf_token_default=03cf1636cd02cb98455735b95a10ee46; passport_mfa_token=CjjAxYHQCxKB95ah2BMkfESU5g%2BRWUo42Jb73bU5wM9lnq5jZek8xpLca73hKusifNXHPMxGAbg2PBpKCjwCBmbDHpt3XqsdoLJc98XGH8GwmrujKVSD%2B%2FnSWZBA99QEXecEgn8uvnh04iM2AUnK6BZda7Kqcc%2B6jB0Ql%2FnoDRj2sdFsIAIiAQP%2Bv2vH; d_ticket=c387aa7a25690088d32e0f295a9faa5828090; odin_tt=adc3604237fa735eef5d9b2726da716e7ea5dd617e5184147618251da72231aa77815405b56975c824529748a009f1f85cf25c9d17928d11725b0ef3d9e34d33; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=54c697b6eb2a02a18133ed41372ee2e5%2C; passport_auth_status_ss=54c697b6eb2a02a18133ed41372ee2e5%2C; sid_guard=9b6a807194da06d0c111bb246ff92247%7C1738946639%7C5184000%7CTue%2C+08-Apr-2025+16%3A43%3A59+GMT; uid_tt=71dd29fd50dbf8f1a1d7b3090d765234; uid_tt_ss=71dd29fd50dbf8f1a1d7b3090d765234; sid_tt=9b6a807194da06d0c111bb246ff92247; sessionid=9b6a807194da06d0c111bb246ff92247; sessionid_ss=9b6a807194da06d0c111bb246ff92247; is_staff_user=false; sid_ucp_v1=1.0.0-KDQ4ODUyZTlhZDk3ZjE1NmUzN2I4NzhkNWQ0MTZhYWU0MTU3YzlhMWUKIAjZtNCbo82jBhDP8Ji9BhjCsR4gDDC57oy9BjgCQPEHGgJscSIgOWI2YTgwNzE5NGRhMDZkMGMxMTFiYjI0NmZmOTIyNDc; ssid_ucp_v1=1.0.0-KDQ4ODUyZTlhZDk3ZjE1NmUzN2I4NzhkNWQ0MTZhYWU0MTU3YzlhMWUKIAjZtNCbo82jBhDP8Ji9BhjCsR4gDDC57oy9BjgCQPEHGgJscSIgOWI2YTgwNzE5NGRhMDZkMGMxMTFiYjI0NmZmOTIyNDc; store-region=cn-cq; store-region-src=uid; gd_random=eyJtYXRjaCI6ZmFsc2UsInBlcmNlbnQiOjAuNTE0NzkyODkzNjg0MzYwOX0=.SVGmqNMa47pjNtmC7+6Wr9q93n5KK+Dqurx7dlvQHZs=; gd_random_1831904=eyJtYXRjaCI6ZmFsc2UsInBlcmNlbnQiOjAuNTE0NzkyODkzNjg0MzYwOX0=.SVGmqNMa47pjNtmC7+6Wr9q93n5KK+Dqurx7dlvQHZs=; ttwid=1%7Cnh818I2twXm5rLksuW8zNJtjX2M-xpfL2fl7tkNB2nU%7C1740007074%7C6cc9d8fedc3428ee0be956084306b10f6c5e0fdfe1d46b6865743b8b0cbf7efd; msToken=jPWWHBmTRz_u_ffFKYe1OzCHLh7A145jL4Szwejn9hi8lB-hy42SY-w9aAI-HIx5vuRdV--WsJsjbimFZBCVbvudGgGabc0Bl6YGf7hEbEZVl1R1Q2d_RKJRQ2afDOM=; passport_fe_beating_status=true; tt_scid=lcMWf5kyIEkN1kagjeVFk3xIlkXuiylDzONDpFUVo5lnNGvXddDS43c0dKRjePzN41c3', 'Host': 'www.doubao.com', 'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/', 'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36', 'x-flow-trace': '04-1352816f0f664005-5e549db4dd9f491d-01', 'last-event-id': 'undefined', 'Agw-Js-Conv': 'str, str', 'X-Requested-With': 'mark.via', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty'}
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 请求体: {"messages": [{"content": "{\"text\": \"\\u641c\\u7d22\\u6296\\u97f3\\u89c6\\u9891:\\u7f8e\\u5973\"}", "content_type": 2001, "attachments": [], "references": []}], "completion_option": {"is_regen": false, "with_suggest": true, "need_create_conversation": false, "launch_stage": 1, "is_replace": false, "is_delete": false, "message_from": 0, "use_deep_think": false, "use_auto_cot": true, "resend_for_regen": false, "event_id": "0"}, "evaluate_option": {"web_ab_params": ""}, "section_id": "381753869244526428", "conversation_id": "38175386924526428", "local_message_id": "ba0d6520-95d5-11f0-a188-0d4dde70dcf1"}
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 请求URL: https://www.doubao.com/samantha/chat/completion?aid=497858&device_id=7468716989062841895&device_plat...
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 请求数据大小: 606 字符
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 收到响应，状态码: 200
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 响应头: {'server': 'Tengine', 'content-type': 'text/event-stream', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'date': 'Wed, 30 Jul 2025 09:54:05 GMT', 'cache-control': 'no-cache', 'x-tt-agw-login': '1', 'x-tt-logid': '2025073017540509C92D1060AA69024471', 'server-timing': 'inner; dur=118,tt_agw; dur=106', 'x-ms-token': 'msQxxbUi_-u13wEOxCDxsCOO458NIWBmVg1x6pk_trhqOBFb8_pjIJH-FrBsormY8Pj_aR6UijriPHubxtdMcop3BxK-98E_R_DnZc4P4SmaUQgWwEc8P48DII5GSzQ=', 'x-envoy-response-flags': '-', 'x-tt-trace-host': '01a904f32dfda387cbf0c81d4c4d78f48a57c7d8f6d38a8c14808bec1094876f84b003cb19f4b5e26465687736e2644843877921d53d1737bb5a727ab2df92780959a188cca9fc75dbe570640d81d5c32cf5102f92c35a7e3c4f44cde72217a42485124bd0917335755941e0c6653a9c327ae4369a14d2a4df67bcacd4937b45d8', 'x-tt-trace-tag': 'id=03;cdn-cache=miss;type=dyn', 'x-tt-trace-id': '00-25073017540509C92D1060AA69024471-77E20D6204B02035-00', 'x-tt-timestamp': '1753869245.892', 'via': 'ens-cache30.cn7025[240,0]', 'timing-allow-origin': '*', 'eagleid': 'db999d3217538692456732723e'}
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 响应内容长度: 1268 字节
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 开始处理响应流数据
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 开始处理SSE响应流
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 响应数据类型: <class 'bytes'>, 大小: 1268 字节
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 解码后数据长度: 1260 字符
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 处理第 1 个事件，数据长度: 1192
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 事件类型: 2005
2025-07-30 17:54:05 | WARNING | [DoubaoVideoSearch] 收到错误事件: {'code': 710022004, 'message': 'rate limited', 'error_detail': {'code': 710022004, 'locale': 'zh', 'message': '系统错误', 'ext': {'decision': '{"code":"10000","from":"shark_admin","version":"","type":"verify","region":"cn","subtype":"semantic_reasoning","detail":"JPAwhqP3F8ppRMIQXTOI62vr*TK0P86DDMf0zZ6QUL2suOsBe*u0SILW5SZrMCbLg7Kz3MHJ3XqSS0US-V4qkmrcP-hEa-O8XEK0oKDKxKqhN5X6O*eaVpQUGtizb1dpxpJNIwQN6oDNUvar4q8e*axFlZdBFrsU*VHTTSCFAFaFViybLvDPWCojwiNlOARoa04zbZ4Bz9rt9Boy-dyvtxo-hXLFoTGUoUvFNE9ZxMtqjh3ur122qjzfqzfImm6EM0gS**H7t-1UZR6CfL*y7CcwM*suJBs-ueBfN2-TOqHnvwHneM3x1vffnXMWepyr-OXQtL-l0d*n0-QkffkDrXJxOfiFWpcmj*2Iqr1sTuJuyNSXjhVSoz3GKyCUTr71XEYLTDo4rCI6E07lOEECCNDUDOLz-6rkQQpyHcWxO8J-FR*xXb5a2jTvtqr6vQzYsmVS38i0rnL70-s0z9ItQ64eEfgAyiOmwoDbIBZrAc7MPo*YjFaYrwe*BjyF","server_sdk_env":"{\\"idc\\":\\"lf\\",\\"region\\":\\"CN\\",\\"server_type\\":\\"business\\"}","log_id":"2025073017540509C92D1060AA69024471","verify_scene":"doubao_message_web"}'}}}
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 处理第 2 个事件，数据长度: 52
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 事件类型: 2003
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 收到结束事件
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 结束事件：返回文本结果
2025-07-30 17:54:05 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: text
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 开始处理搜索结果
2025-07-30 17:54:05 | DEBUG | [DoubaoVideoSearch] 开始处理视频消息，结果类型: text
2025-07-30 17:54:05 | WARNING | [DoubaoVideoSearch] 结果类型不是视频: text
2025-07-30 17:54:06 | INFO | 发送文字消息: 对方wxid:***********@chatroom at: 内容:这个不行
2025-07-30 17:54:06 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 1.01秒
2025-07-30 17:54:06 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 17:54:06 | DEBUG | 处理消息内容: '找视频 美女'
2025-07-30 17:54:06 | DEBUG | 消息内容 '找视频 美女' 不匹配任何命令，忽略
