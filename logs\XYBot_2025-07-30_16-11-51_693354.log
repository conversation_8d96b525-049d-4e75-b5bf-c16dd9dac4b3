2025-07-30 16:11:52 | SUCCESS | 读取主设置成功
2025-07-30 16:11:52 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-30 16:11:53 | INFO | 2025/07/30 16:11:53 GetRedisAddr: 127.0.0.1:6379
2025-07-30 16:11:53 | INFO | 2025/07/30 16:11:53 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-30 16:11:53 | INFO | 2025/07/30 16:11:53 Server start at :9000
2025-07-30 16:11:53 | SUCCESS | WechatAPI服务已启动
2025-07-30 16:11:54 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-30 16:11:54 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-30 16:11:54 | SUCCESS | 登录成功
2025-07-30 16:11:54 | SUCCESS | 已开启自动心跳
2025-07-30 16:11:54 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 16:11:54 | SUCCESS | 数据库初始化成功
2025-07-30 16:11:54 | SUCCESS | 定时任务已启动
2025-07-30 16:11:54 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-30 16:11:54 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 16:11:54 | INFO | 播客API初始化成功
2025-07-30 16:11:54 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 16:11:54 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 16:11:54 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-30 16:11:54 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-30 16:11:54 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-30 16:11:54 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-30 16:11:54 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-30 16:11:54 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-30 16:11:54 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-30 16:11:55 | INFO | [ChatSummary] 数据库初始化成功
2025-07-30 16:11:55 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-30 16:11:55 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-30 16:11:55 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-30 16:11:55 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-30 16:11:55 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-30 16:11:55 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-30 16:11:55 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-30 16:11:55 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 16:11:55 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-30 16:11:55 | INFO | [RenameReminder] 开始启用插件...
2025-07-30 16:11:55 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-30 16:11:55 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-30 16:11:55 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-30 16:11:55 | INFO | 已设置检查间隔为 3600 秒
2025-07-30 16:11:55 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-30 16:11:55 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-30 16:11:56 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-30 16:11:56 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-30 16:11:56 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-30 16:11:57 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-30 16:11:57 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 16:11:57 | INFO | [yuanbao] 插件初始化完成
2025-07-30 16:11:57 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-30 16:11:57 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-30 16:11:57 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-30 16:11:57 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-30 16:11:57 | INFO | 处理堆积消息中
2025-07-30 16:11:57 | SUCCESS | 处理堆积消息完毕
2025-07-30 16:11:57 | SUCCESS | 开始处理消息
2025-07-30 16:14:09 | DEBUG | 收到消息: {'MsgId': 1298012573, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753863262, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_+fvXy37n|v1_hxfXofg7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女系列', 'NewMsgId': 6525952430108083054, 'MsgSeq': 871412020}
2025-07-30 16:14:09 | INFO | 收到文本消息: 消息ID:1298012573 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女系列
2025-07-30 16:14:10 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女系列 from wxid_ubbh6q832tcs21
2025-07-30 16:14:10 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女系列
2025-07-30 16:14:10 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 16:14:10 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: text
2025-07-30 16:14:10 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:出问题了
2025-07-30 16:14:10 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 0.66秒
2025-07-30 16:14:10 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 16:14:10 | DEBUG | 处理消息内容: '找视频 美女系列'
2025-07-30 16:14:10 | DEBUG | 消息内容 '找视频 美女系列' 不匹配任何命令，忽略
2025-07-30 16:14:50 | DEBUG | 收到消息: {'MsgId': 1988654153, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cb2tqz5n94lp12:\n倒闭吧干节奏大师去'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753863303, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_hVCZbej7|v1_vVj2Bn7X</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6485057417104870666, 'MsgSeq': 871412023}
2025-07-30 16:14:50 | INFO | 收到文本消息: 消息ID:1988654153 来自:27852221909@chatroom 发送人:wxid_cb2tqz5n94lp12 @:[] 内容:倒闭吧干节奏大师去
2025-07-30 16:14:50 | DEBUG | 处理消息内容: '倒闭吧干节奏大师去'
2025-07-30 16:14:50 | DEBUG | 消息内容 '倒闭吧干节奏大师去' 不匹配任何命令，忽略
2025-07-30 16:15:41 | DEBUG | 收到消息: {'MsgId': 1075239218, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_rwfb9vuy93jn22:\n找文案 骂策划系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753863354, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_/1DR3p7g|v1_5NIOlMIR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '黹忙 : 找文案 骂策划系列', 'NewMsgId': 5307233543491517497, 'MsgSeq': 871412024}
2025-07-30 16:15:41 | INFO | 收到文本消息: 消息ID:1075239218 来自:55878994168@chatroom 发送人:wxid_rwfb9vuy93jn22 @:[] 内容:找文案 骂策划系列
2025-07-30 16:15:42 | DEBUG | 处理消息内容: '找文案 骂策划系列'
2025-07-30 16:15:42 | DEBUG | 消息内容 '找文案 骂策划系列' 不匹配任何命令，忽略
2025-07-30 16:17:53 | DEBUG | 收到消息: {'MsgId': 1819546431, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_rwfb9vuy93jn22:\n@瑶瑶\u2005找文案 骂策划系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753863486, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_4usgcju5ey9q29]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_mmSMBUEo|v1_ggOexes0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '黹忙在群聊中@了你', 'NewMsgId': 623692192703385941, 'MsgSeq': 871412025}
2025-07-30 16:17:53 | INFO | 收到被@消息: 消息ID:1819546431 来自:55878994168@chatroom 发送人:wxid_rwfb9vuy93jn22 @:['wxid_4usgcju5ey9q29'] 内容:@瑶瑶 找文案 骂策划系列
2025-07-30 16:18:06 | DEBUG | 收到消息: {'MsgId': 1272632407, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n即梦 画个美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753863499, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_+Q3nLDzx|v1_WD9DWo9D</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 即梦 画个美女', 'NewMsgId': 3781639680335167918, 'MsgSeq': 871412026}
2025-07-30 16:18:06 | INFO | 收到文本消息: 消息ID:1272632407 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:即梦 画个美女
2025-07-30 16:18:07 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:收到
2025-07-30 16:18:33 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-30 16:18:35 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-30 16:18:37 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-30 16:18:39 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-30 16:18:39 | DEBUG | 处理消息内容: '即梦 画个美女'
2025-07-30 16:18:39 | DEBUG | 消息内容 '即梦 画个美女' 不匹配任何命令，忽略
2025-07-30 16:19:33 | DEBUG | [TempFileManager] 已清理文件: C:\Users\<USER>\AppData\Local\Temp\jiemeng_0_w8uu_yrd.jpg
2025-07-30 16:19:35 | DEBUG | [TempFileManager] 已清理文件: C:\Users\<USER>\AppData\Local\Temp\jiemeng_1_pwfdx1u2.jpg
2025-07-30 16:19:37 | DEBUG | [TempFileManager] 已清理文件: C:\Users\<USER>\AppData\Local\Temp\jiemeng_2_3azuh9c_.jpg
2025-07-30 16:19:39 | DEBUG | [TempFileManager] 已清理文件: C:\Users\<USER>\AppData\Local\Temp\jiemeng_3_5mkbrkpj.jpg
2025-07-30 16:22:54 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="7cf1ae63564bbcdf7716b748709514c5" encryver="1" cdnthumbaeskey="7cf1ae63564bbcdf7716b748709514c5" cdnthumburl="3057020100044b30490201000204f060aea202034c57c102044a4fe17c02046889d66b042430663039343934332d636263312d343862662d393031342d653361303338333261636533020405290a020201000405004c57c100" cdnthumblength="3211" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202034c57c102044a4fe17c02046889d66b042430663039343934332d636263312d343862662d393031342d653361303338333261636533020405290a020201000405004c57c100" length="323645" md5="0f71af85a9b708004ddc0ecf73e77482" hevc_mid_size="34439" originsourcemd5="71338bac3064be1c7e8c314fec733a03">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUwMTAxMDEwMTAyMDAwMDAiLCJwZHFIYXNoIjoiZTc2MDI0NzI3YjQzNjM2YjM0\nMzJlNmI0Y2FhZDM2YjU3NGI2OWI2OWM5NGE2NTUyMTk0YWNiNjkzNTk2OTRiNSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753863787, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>835fbfe483482a63df07dc85fa34c43f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_KYQa87/v|v1_7/g8GPFg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1438969824839121666, 'MsgSeq': 871412033}
2025-07-30 16:22:54 | INFO | 收到图片消息: 消息ID:********* 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 XML:<?xml version="1.0"?><msg><img aeskey="7cf1ae63564bbcdf7716b748709514c5" encryver="1" cdnthumbaeskey="7cf1ae63564bbcdf7716b748709514c5" cdnthumburl="3057020100044b30490201000204f060aea202034c57c102044a4fe17c02046889d66b042430663039343934332d636263312d343862662d393031342d653361303338333261636533020405290a020201000405004c57c100" cdnthumblength="3211" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202034c57c102044a4fe17c02046889d66b042430663039343934332d636263312d343862662d393031342d653361303338333261636533020405290a020201000405004c57c100" length="323645" md5="0f71af85a9b708004ddc0ecf73e77482" hevc_mid_size="34439" originsourcemd5="71338bac3064be1c7e8c314fec733a03"><secHashInfoBase64>eyJwaGFzaCI6IjUwMTAxMDEwMTAyMDAwMDAiLCJwZHFIYXNoIjoiZTc2MDI0NzI3YjQzNjM2YjM0MzJlNmI0Y2FhZDM2YjU3NGI2OWI2OWM5NGE2NTUyMTk0YWNiNjkzNTk2OTRiNSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 16:22:54 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-07-30 16:22:54 | INFO | [TimerTask] 缓存图片消息: *********
2025-07-30 16:23:36 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n倒闭了玩别的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753863829, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_CpCO6pu8|v1_WXIZHHzX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5120051675486070341, 'MsgSeq': 871412034}
2025-07-30 16:23:36 | INFO | 收到文本消息: 消息ID:********* 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:倒闭了玩别的
2025-07-30 16:23:37 | DEBUG | 处理消息内容: '倒闭了玩别的'
2025-07-30 16:23:37 | DEBUG | 消息内容 '倒闭了玩别的' 不匹配任何命令，忽略
2025-07-30 16:25:41 | DEBUG | 收到消息: {'MsgId': 1373818283, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>那是有原因的[捂脸]，出bug一直可以点亮领钻石，你没经历啊？</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>7175066191318864269</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_2530z9t0joek22</chatusr>\n\t\t\t<displayname>慕ؓ悦ؓ˒</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;4&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;935d012094e49c13f9f5afd91a4e107f_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_T8MUMp2t|v1_wNKYKANv&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;appmsg appid="" sdkver="0"&gt;\n\t\t&lt;title&gt;真的会扣走啊&lt;/title&gt;\n\t\t&lt;des /&gt;\n\t\t&lt;username /&gt;\n\t\t&lt;action&gt;view&lt;/action&gt;\n\t\t&lt;type&gt;57&lt;/type&gt;\n\t\t&lt;showtype&gt;0&lt;/showtype&gt;\n\t\t&lt;content /&gt;\n\t\t&lt;url /&gt;\n\t\t&lt;lowurl /&gt;\n\t\t&lt;forwardflag&gt;0&lt;/forwardflag&gt;\n\t\t&lt;dataurl /&gt;\n\t\t&lt;lowdataurl /&gt;\n\t\t&lt;contentattr&gt;0&lt;/contentattr&gt;\n\t\t&lt;streamvideo&gt;\n\t\t\t&lt;streamvideourl /&gt;\n\t\t\t&lt;streamvideototaltime&gt;0&lt;/streamvideototaltime&gt;\n\t\t\t&lt;streamvideotitle /&gt;\n\t\t\t&lt;streamvideowording /&gt;\n\t\t\t&lt;streamvideoweburl /&gt;\n\t\t\t&lt;streamvideothumburl /&gt;\n\t\t\t&lt;streamvideoaduxinfo /&gt;\n\t\t\t&lt;streamvideopublishid /&gt;\n\t\t&lt;/streamvideo&gt;\n\t\t&lt;canvasPageItem&gt;\n\t\t\t&lt;canvasPageXml&gt;&lt;![CDATA[]]&gt;&lt;/canvasPageXml&gt;\n\t\t&lt;/canvasPageItem&gt;\n\t\t&lt;refermsg&gt;&lt;/refermsg&gt;\n\t\t&lt;appattach&gt;\n\t\t\t&lt;totallen&gt;0&lt;/totallen&gt;\n\t\t\t&lt;attachid /&gt;\n\t\t\t&lt;cdnattachurl /&gt;\n\t\t\t&lt;emoticonmd5 /&gt;\n\t\t\t&lt;aeskey /&gt;\n\t\t\t&lt;fileext /&gt;\n\t\t\t&lt;islargefilemsg&gt;0&lt;/islargefilemsg&gt;\n\t\t&lt;/appattach&gt;\n\t\t&lt;extinfo /&gt;\n\t\t&lt;androidsource&gt;0&lt;/androidsource&gt;\n\t\t&lt;thumburl /&gt;\n\t\t&lt;mediatagname /&gt;\n\t\t&lt;messageaction&gt;&lt;![CDATA[]]&gt;&lt;/messageaction&gt;\n\t\t&lt;messageext&gt;&lt;![CDATA[]]&gt;&lt;/messageext&gt;\n\t\t&lt;emoticongift&gt;\n\t\t\t&lt;packageflag&gt;0&lt;/packageflag&gt;\n\t\t\t&lt;packageid /&gt;\n\t\t&lt;/emoticongift&gt;\n\t\t&lt;emoticonshared&gt;\n\t\t\t&lt;packageflag&gt;0&lt;/packageflag&gt;\n\t\t\t&lt;packageid /&gt;\n\t\t&lt;/emoticonshared&gt;\n\t\t&lt;designershared&gt;\n\t\t\t&lt;designeruin&gt;0&lt;/designeruin&gt;\n\t\t\t&lt;designername&gt;null&lt;/designername&gt;\n\t\t\t&lt;designerrediretcturl&gt;&lt;![CDATA[null]]&gt;&lt;/designerrediretcturl&gt;\n\t\t&lt;/designershared&gt;\n\t\t&lt;emotionpageshared&gt;\n\t\t\t&lt;tid&gt;0&lt;/tid&gt;\n\t\t\t&lt;title&gt;null&lt;/title&gt;\n\t\t\t&lt;desc&gt;null&lt;/desc&gt;\n\t\t\t&lt;iconUrl&gt;&lt;![CDATA[null]]&gt;&lt;/iconUrl&gt;\n\t\t\t&lt;secondUrl&gt;null&lt;/secondUrl&gt;\n\t\t\t&lt;pageType&gt;0&lt;/pageType&gt;\n\t\t\t&lt;setKey&gt;null&lt;/setKey&gt;\n\t\t&lt;/emotionpageshared&gt;\n\t\t&lt;webviewshared&gt;\n\t\t\t&lt;shareUrlOriginal /&gt;\n\t\t\t&lt;shareUrlOpen /&gt;\n\t\t\t&lt;jsAppId /&gt;\n\t\t\t&lt;publisherId /&gt;\n\t\t\t&lt;publisherReqId /&gt;\n\t\t&lt;/webviewshared&gt;\n\t\t&lt;template_id /&gt;\n\t\t&lt;md5 /&gt;\n\t\t&lt;websearch&gt;\n\t\t\t&lt;rec_category&gt;0&lt;/rec_category&gt;\n\t\t\t&lt;channelId&gt;0&lt;/channelId&gt;\n\t\t&lt;/websearch&gt;\n\t\t&lt;weappinfo&gt;\n\t\t\t&lt;username /&gt;\n\t\t\t&lt;appid /&gt;\n\t\t\t&lt;appservicetype&gt;0&lt;/appservicetype&gt;\n\t\t\t&lt;secflagforsinglepagemode&gt;0&lt;/secflagforsinglepagemode&gt;\n\t\t\t&lt;videopageinfo&gt;\n\t\t\t\t&lt;thumbwidth&gt;0&lt;/thumbwidth&gt;\n\t\t\t\t&lt;thumbheight&gt;0&lt;/thumbheight&gt;\n\t\t\t\t&lt;fromopensdk&gt;0&lt;/fromopensdk&gt;\n\t\t\t&lt;/videopageinfo&gt;\n\t\t&lt;/weappinfo&gt;\n\t\t&lt;statextstr /&gt;\n\t\t&lt;musicShareItem&gt;\n\t\t\t&lt;musicDuration&gt;0&lt;/musicDuration&gt;\n\t\t&lt;/musicShareItem&gt;\n\t\t&lt;finderLiveProductShare&gt;\n\t\t\t&lt;finderLiveID&gt;&lt;![CDATA[]]&gt;&lt;/finderLiveID&gt;\n\t\t\t&lt;finderUsername&gt;&lt;![CDATA[]]&gt;&lt;/finderUsername&gt;\n\t\t\t&lt;finderObjectID&gt;&lt;![CDATA[]]&gt;&lt;/finderObjectID&gt;\n\t\t\t&lt;finderNonceID&gt;&lt;![CDATA[]]&gt;&lt;/finderNonceID&gt;\n\t\t\t&lt;liveStatus&gt;&lt;![CDATA[]]&gt;&lt;/liveStatus&gt;\n\t\t\t&lt;appId&gt;&lt;![CDATA[]]&gt;&lt;/appId&gt;\n\t\t\t&lt;pagePath&gt;&lt;![CDATA[]]&gt;&lt;/pagePath&gt;\n\t\t\t&lt;productId&gt;&lt;![CDATA[]]&gt;&lt;/productId&gt;\n\t\t\t&lt;coverUrl&gt;&lt;![CDATA[]]&gt;&lt;/coverUrl&gt;\n\t\t\t&lt;productTitle&gt;&lt;![CDATA[]]&gt;&lt;/productTitle&gt;\n\t\t\t&lt;marketPrice&gt;&lt;![CDATA[0]]&gt;&lt;/marketPrice&gt;\n\t\t\t&lt;sellingPrice&gt;&lt;![CDATA[0]]&gt;&lt;/sellingPrice&gt;\n\t\t\t&lt;platformHeadImg&gt;&lt;![CDATA[]]&gt;&lt;/platformHeadImg&gt;\n\t\t\t&lt;platformName&gt;&lt;![CDATA[]]&gt;&lt;/platformName&gt;\n\t\t\t&lt;shopWindowId&gt;&lt;![CDATA[]]&gt;&lt;/shopWindowId&gt;\n\t\t\t&lt;flashSalePrice&gt;&lt;![CDATA[0]]&gt;&lt;/flashSalePrice&gt;\n\t\t\t&lt;flashSaleEndTime&gt;&lt;![CDATA[0]]&gt;&lt;/flashSaleEndTime&gt;\n\t\t\t&lt;ecSource&gt;&lt;![CDATA[]]&gt;&lt;/ecSource&gt;\n\t\t\t&lt;sellingPriceWording&gt;&lt;![CDATA[]]&gt;&lt;/sellingPriceWording&gt;\n\t\t\t&lt;platformIconURL&gt;&lt;![CDATA[]]&gt;&lt;/platformIconURL&gt;\n\t\t\t&lt;firstProductTagURL&gt;&lt;![CDATA[]]&gt;&lt;/firstProductTagURL&gt;\n\t\t\t&lt;firstProductTagAspectRatioString&gt;&lt;![CDATA[0.0]]&gt;&lt;/firstProductTagAspectRatioString&gt;\n\t\t\t&lt;secondProductTagURL&gt;&lt;![CDATA[]]&gt;&lt;/secondProductTagURL&gt;\n\t\t\t&lt;secondProductTagAspectRatioString&gt;&lt;![CDATA[0.0]]&gt;&lt;/secondProductTagAspectRatioString&gt;\n\t\t\t&lt;firstGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/firstGuaranteeWording&gt;\n\t\t\t&lt;secondGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/secondGuaranteeWording&gt;\n\t\t\t&lt;thirdGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/thirdGuaranteeWording&gt;\n\t\t\t&lt;isPriceBeginShow&gt;false&lt;/isPriceBeginShow&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t\t&lt;promoterKey&gt;&lt;![CDATA[]]&gt;&lt;/promoterKey&gt;\n\t\t\t&lt;discountWording&gt;&lt;![CDATA[]]&gt;&lt;/discountWording&gt;\n\t\t\t&lt;priceSuffixDescription&gt;&lt;![CDATA[]]&gt;&lt;/priceSuffixDescription&gt;\n\t\t\t&lt;productCardKey&gt;&lt;![CDATA[]]&gt;&lt;/productCardKey&gt;\n\t\t\t&lt;isWxShop&gt;&lt;![CDATA[]]&gt;&lt;/isWxShop&gt;\n\t\t\t&lt;brandIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/brandIconUrl&gt;\n\t\t\t&lt;rIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrl&gt;\n\t\t\t&lt;rIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrlDarkMode&gt;\n\t\t\t&lt;topShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrl&gt;\n\t\t\t&lt;topShopIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrlDarkMode&gt;\n\t\t\t&lt;simplifyTopShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrl&gt;\n\t\t\t&lt;simplifyTopShopIconUrlDarkmode&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrlDarkmode&gt;\n\t\t\t&lt;topShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconWidth&gt;\n\t\t\t&lt;topShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconHeight&gt;\n\t\t\t&lt;simplifyTopShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconWidth&gt;\n\t\t\t&lt;simplifyTopShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconHeight&gt;\n\t\t\t&lt;showBoxItemStringList /&gt;\n\t\t&lt;/finderLiveProductShare&gt;\n\t\t&lt;finderOrder&gt;\n\t\t\t&lt;appID&gt;&lt;![CDATA[]]&gt;&lt;/appID&gt;\n\t\t\t&lt;orderID&gt;&lt;![CDATA[]]&gt;&lt;/orderID&gt;\n\t\t\t&lt;path&gt;&lt;![CDATA[]]&gt;&lt;/path&gt;\n\t\t\t&lt;priceWording&gt;&lt;![CDATA[]]&gt;&lt;/priceWording&gt;\n\t\t\t&lt;stateWording&gt;&lt;![CDATA[]]&gt;&lt;/stateWording&gt;\n\t\t\t&lt;productImageURL&gt;&lt;![CDATA[]]&gt;&lt;/productImageURL&gt;\n\t\t\t&lt;products&gt;&lt;![CDATA[]]&gt;&lt;/products&gt;\n\t\t\t&lt;productsCount&gt;&lt;![CDATA[0]]&gt;&lt;/productsCount&gt;\n\t\t\t&lt;orderType&gt;&lt;![CDATA[0]]&gt;&lt;/orderType&gt;\n\t\t\t&lt;newPriceWording&gt;&lt;![CDATA[]]&gt;&lt;/newPriceWording&gt;\n\t\t\t&lt;newStateWording&gt;&lt;![CDATA[]]&gt;&lt;/newStateWording&gt;\n\t\t\t&lt;useNewWording&gt;&lt;![CDATA[0]]&gt;&lt;/useNewWording&gt;\n\t\t&lt;/finderOrder&gt;\n\t\t&lt;finderShopWindowShare&gt;\n\t\t\t&lt;finderUsername&gt;&lt;![CDATA[]]&gt;&lt;/finderUsername&gt;\n\t\t\t&lt;avatar&gt;&lt;![CDATA[]]&gt;&lt;/avatar&gt;\n\t\t\t&lt;nickname&gt;&lt;![CDATA[]]&gt;&lt;/nickname&gt;\n\t\t\t&lt;commodityInStockCount&gt;&lt;![CDATA[]]&gt;&lt;/commodityInStockCount&gt;\n\t\t\t&lt;appId&gt;&lt;![CDATA[]]&gt;&lt;/appId&gt;\n\t\t\t&lt;path&gt;&lt;![CDATA[]]&gt;&lt;/path&gt;\n\t\t\t&lt;appUsername&gt;&lt;![CDATA[]]&gt;&lt;/appUsername&gt;\n\t\t\t&lt;query&gt;&lt;![CDATA[]]&gt;&lt;/query&gt;\n\t\t\t&lt;liteAppId&gt;&lt;![CDATA[]]&gt;&lt;/liteAppId&gt;\n\t\t\t&lt;liteAppPath&gt;&lt;![CDATA[]]&gt;&lt;/liteAppPath&gt;\n\t\t\t&lt;liteAppQuery&gt;&lt;![CDATA[]]&gt;&lt;/liteAppQuery&gt;\n\t\t\t&lt;platformTagURL&gt;&lt;![CDATA[]]&gt;&lt;/platformTagURL&gt;\n\t\t\t&lt;saleWording&gt;&lt;![CDATA[]]&gt;&lt;/saleWording&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t\t&lt;profileTypeWording&gt;&lt;![CDATA[]]&gt;&lt;/profileTypeWording&gt;\n\t\t\t&lt;saleWordingExtra&gt;&lt;![CDATA[]]&gt;&lt;/saleWordingExtra&gt;\n\t\t\t&lt;isWxShop&gt;&lt;![CDATA[]]&gt;&lt;/isWxShop&gt;\n\t\t\t&lt;platformIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/platformIconUrl&gt;\n\t\t\t&lt;brandIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/brandIconUrl&gt;\n\t\t\t&lt;description&gt;&lt;![CDATA[]]&gt;&lt;/description&gt;\n\t\t\t&lt;backgroundUrl&gt;&lt;![CDATA[]]&gt;&lt;/backgroundUrl&gt;\n\t\t\t&lt;darkModePlatformIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/darkModePlatformIconUrl&gt;\n\t\t\t&lt;rIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrl&gt;\n\t\t\t&lt;rIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrlDarkMode&gt;\n\t\t\t&lt;rWords&gt;&lt;![CDATA[]]&gt;&lt;/rWords&gt;\n\t\t\t&lt;topShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrl&gt;\n\t\t\t&lt;topShopIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrlDarkMode&gt;\n\t\t\t&lt;simplifyTopShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrl&gt;\n\t\t\t&lt;simplifyTopShopIconUrlDarkmode&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrlDarkmode&gt;\n\t\t\t&lt;topShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconWidth&gt;\n\t\t\t&lt;topShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconHeight&gt;\n\t\t\t&lt;simplifyTopShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconWidth&gt;\n\t\t\t&lt;simplifyTopShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconHeight&gt;\n\t\t\t&lt;reputationInfo&gt;\n\t\t\t\t&lt;hasReputationInfo&gt;0&lt;/hasReputationInfo&gt;\n\t\t\t\t&lt;reputationScore&gt;0&lt;/reputationScore&gt;\n\t\t\t\t&lt;reputationWording /&gt;\n\t\t\t\t&lt;reputationTextColor /&gt;\n\t\t\t\t&lt;reputationLevelWording /&gt;\n\t\t\t\t&lt;reputationBackgroundColor /&gt;\n\t\t\t&lt;/reputationInfo&gt;\n\t\t\t&lt;productImageURLList /&gt;\n\t\t&lt;/finderShopWindowShare&gt;\n\t\t&lt;findernamecard&gt;\n\t\t\t&lt;username /&gt;\n\t\t\t&lt;avatar&gt;&lt;![CDATA[]]&gt;&lt;/avatar&gt;\n\t\t\t&lt;nickname /&gt;\n\t\t\t&lt;auth_job /&gt;\n\t\t\t&lt;auth_icon&gt;0&lt;/auth_icon&gt;\n\t\t\t&lt;auth_icon_url /&gt;\n\t\t\t&lt;ecSource&gt;&lt;![CDATA[]]&gt;&lt;/ecSource&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t&lt;/findernamecard&gt;\n\t\t&lt;finderGuarantee&gt;\n\t\t\t&lt;scene&gt;&lt;![CDATA[0]]&gt;&lt;/scene&gt;\n\t\t&lt;/finderGuarantee&gt;\n\t\t&lt;directshare&gt;0&lt;/directshare&gt;\n\t\t&lt;gamecenter&gt;\n\t\t\t&lt;namecard&gt;\n\t\t\t\t&lt;iconUrl /&gt;\n\t\t\t\t&lt;name /&gt;\n\t\t\t\t&lt;desc /&gt;\n\t\t\t\t&lt;tail /&gt;\n\t\t\t\t&lt;jumpUrl /&gt;\n\t\t\t\t&lt;liteappId /&gt;\n\t\t\t\t&lt;liteappPath /&gt;\n\t\t\t\t&lt;liteappQuery /&gt;\n\t\t\t\t&lt;liteappMinVersion /&gt;\n\t\t\t&lt;/namecard&gt;\n\t\t&lt;/gamecenter&gt;\n\t\t&lt;patMsg&gt;\n\t\t\t&lt;chatUser /&gt;\n\t\t\t&lt;records&gt;\n\t\t\t\t&lt;recordNum&gt;0&lt;/recordNum&gt;\n\t\t\t&lt;/records&gt;\n\t\t&lt;/patMsg&gt;\n\t\t&lt;secretmsg&gt;\n\t\t\t&lt;issecretmsg&gt;0&lt;/issecretmsg&gt;\n\t\t&lt;/secretmsg&gt;\n\t\t&lt;referfromscene&gt;0&lt;/referfromscene&gt;\n\t\t&lt;gameshare&gt;\n\t\t\t&lt;liteappext&gt;\n\t\t\t\t&lt;liteappbizdata /&gt;\n\t\t\t\t&lt;priority&gt;0&lt;/priority&gt;\n\t\t\t&lt;/liteappext&gt;\n\t\t\t&lt;appbrandext&gt;\n\t\t\t\t&lt;litegameinfo /&gt;\n\t\t\t\t&lt;priority&gt;-1&lt;/priority&gt;\n\t\t\t&lt;/appbrandext&gt;\n\t\t\t&lt;gameshareid /&gt;\n\t\t\t&lt;sharedata /&gt;\n\t\t\t&lt;isvideo&gt;0&lt;/isvideo&gt;\n\t\t\t&lt;duration&gt;-1&lt;/duration&gt;\n\t\t\t&lt;isexposed&gt;0&lt;/isexposed&gt;\n\t\t\t&lt;readtext /&gt;\n\t\t&lt;/gameshare&gt;\n\t\t&lt;tingChatRoomItem&gt;\n\t\t\t&lt;type&gt;0&lt;/type&gt;\n\t\t\t&lt;categoryItem&gt;null&lt;/categoryItem&gt;\n\t\t\t&lt;categoryId /&gt;\n\t\t&lt;/tingChatRoomItem&gt;\n\t\t&lt;mpsharetrace&gt;\n\t\t\t&lt;hasfinderelement&gt;0&lt;/hasfinderelement&gt;\n\t\t\t&lt;lastgmsgid /&gt;\n\t\t&lt;/mpsharetrace&gt;\n\t\t&lt;wxgamecard&gt;\n\t\t\t&lt;framesetname /&gt;\n\t\t\t&lt;mbcarddata /&gt;\n\t\t\t&lt;minpkgversion /&gt;\n\t\t\t&lt;clientextinfo /&gt;\n\t\t\t&lt;mbcardheight&gt;0&lt;/mbcardheight&gt;\n\t\t\t&lt;isoldversion&gt;0&lt;/isoldversion&gt;\n\t\t&lt;/wxgamecard&gt;\n\t\t&lt;ecskfcard&gt;\n\t\t\t&lt;framesetname /&gt;\n\t\t\t&lt;mbcarddata /&gt;\n\t\t\t&lt;minupdateunixtimestamp&gt;0&lt;/minupdateunixtimestamp&gt;\n\t\t\t&lt;needheader&gt;false&lt;/needheader&gt;\n\t\t\t&lt;summary /&gt;\n\t\t&lt;/ecskfcard&gt;\n\t\t&lt;liteapp&gt;\n\t\t\t&lt;id&gt;null&lt;/id&gt;\n\t\t\t&lt;path /&gt;\n\t\t\t&lt;query /&gt;\n\t\t\t&lt;istransparent&gt;0&lt;/istransparent&gt;\n\t\t\t&lt;hideicon&gt;0&lt;/hideicon&gt;\n\t\t\t&lt;forbidforward&gt;0&lt;/forbidforward&gt;\n\t\t&lt;/liteapp&gt;\n\t\t&lt;opensdk_share_is_modified&gt;0&lt;/opensdk_share_is_modified&gt;\n\t&lt;/appmsg&gt;\n\t&lt;fromusername&gt;wxid_2530z9t0joek22&lt;/fromusername&gt;\n\t&lt;scene&gt;0&lt;/scene&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;version&gt;1&lt;/version&gt;\n\t\t&lt;appname&gt;&lt;/appname&gt;\n\t&lt;/appinfo&gt;\n\t&lt;commenturl&gt;&lt;/commenturl&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753862623</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ctp9qffuf14b21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753863953, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>935d012094e49c13f9f5afd91a4e107f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Rl8YIF/y|v1_jClYXA4f</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 923018609470436070, 'MsgSeq': 871412035}
2025-07-30 16:25:41 | DEBUG | 从群聊消息中提取发送者: wxid_ctp9qffuf14b21
2025-07-30 16:25:41 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 16:25:41 | INFO | 收到引用消息: 消息ID:1373818283 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 内容:那是有原因的[捂脸]，出bug一直可以点亮领钻石，你没经历啊 引用类型:49
2025-07-30 16:25:41 | INFO | [DouBaoImageToImage] 收到引用消息: 那是有原因的[捂脸]，出bug一直可以点亮领钻石，你没经历啊？
2025-07-30 16:25:41 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 16:25:41 | INFO |   - 消息内容: 那是有原因的[捂脸]，出bug一直可以点亮领钻石，你没经历啊？
2025-07-30 16:25:41 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-30 16:25:41 | INFO |   - 发送人: wxid_ctp9qffuf14b21
2025-07-30 16:25:41 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>真的会扣走啊</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg></refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_2530z9t0joek22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '7175066191318864269', 'NewMsgId': '7175066191318864269', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '慕ؓ悦ؓ˒', 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>935d012094e49c13f9f5afd91a4e107f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_T8MUMp2t|v1_wNKYKANv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753862623', 'SenderWxid': 'wxid_ctp9qffuf14b21'}
2025-07-30 16:25:41 | INFO |   - 引用消息ID: 
2025-07-30 16:25:41 | INFO |   - 引用消息类型: 
2025-07-30 16:25:41 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>真的会扣走啊</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg></refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_2530z9t0joek22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 16:25:41 | INFO |   - 引用消息发送人: wxid_ctp9qffuf14b21
2025-07-30 16:26:03 | DEBUG | 收到消息: {'MsgId': 32677038, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n那我没有'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753863976, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_DoSzMoIg|v1_x4vJcIft</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3879960049316116415, 'MsgSeq': 871412036}
2025-07-30 16:26:03 | INFO | 收到文本消息: 消息ID:32677038 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:那我没有
2025-07-30 16:26:03 | DEBUG | 处理消息内容: '那我没有'
2025-07-30 16:26:03 | DEBUG | 消息内容 '那我没有' 不匹配任何命令，忽略
2025-07-30 16:26:15 | DEBUG | 收到消息: {'MsgId': 2025173132, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>我还没遇到过</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>923018609470436070</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ctp9qffuf14b21</chatusr>\n\t\t\t<displayname>Z⁰</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;4&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;935d012094e49c13f9f5afd91a4e107f_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_MsCxTCo4|v1_UgscGF+3&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;appmsg appid="" sdkver="0"&gt;\n\t\t&lt;title&gt;那是有原因的[捂脸]，出bug一直可以点亮领钻石，你没经历啊？&lt;/title&gt;\n\t\t&lt;des /&gt;\n\t\t&lt;username /&gt;\n\t\t&lt;action&gt;view&lt;/action&gt;\n\t\t&lt;type&gt;57&lt;/type&gt;\n\t\t&lt;showtype&gt;0&lt;/showtype&gt;\n\t\t&lt;content /&gt;\n\t\t&lt;url /&gt;\n\t\t&lt;lowurl /&gt;\n\t\t&lt;forwardflag&gt;0&lt;/forwardflag&gt;\n\t\t&lt;dataurl /&gt;\n\t\t&lt;lowdataurl /&gt;\n\t\t&lt;contentattr&gt;0&lt;/contentattr&gt;\n\t\t&lt;streamvideo&gt;\n\t\t\t&lt;streamvideourl /&gt;\n\t\t\t&lt;streamvideototaltime&gt;0&lt;/streamvideototaltime&gt;\n\t\t\t&lt;streamvideotitle /&gt;\n\t\t\t&lt;streamvideowording /&gt;\n\t\t\t&lt;streamvideoweburl /&gt;\n\t\t\t&lt;streamvideothumburl /&gt;\n\t\t\t&lt;streamvideoaduxinfo /&gt;\n\t\t\t&lt;streamvideopublishid /&gt;\n\t\t&lt;/streamvideo&gt;\n\t\t&lt;canvasPageItem&gt;\n\t\t\t&lt;canvasPageXml&gt;&lt;![CDATA[]]&gt;&lt;/canvasPageXml&gt;\n\t\t&lt;/canvasPageItem&gt;\n\t\t&lt;refermsg&gt;&lt;/refermsg&gt;\n\t\t&lt;appattach&gt;\n\t\t\t&lt;totallen&gt;0&lt;/totallen&gt;\n\t\t\t&lt;attachid /&gt;\n\t\t\t&lt;cdnattachurl /&gt;\n\t\t\t&lt;emoticonmd5 /&gt;\n\t\t\t&lt;aeskey /&gt;\n\t\t\t&lt;fileext /&gt;\n\t\t\t&lt;islargefilemsg&gt;0&lt;/islargefilemsg&gt;\n\t\t&lt;/appattach&gt;\n\t\t&lt;extinfo /&gt;\n\t\t&lt;androidsource&gt;0&lt;/androidsource&gt;\n\t\t&lt;thumburl /&gt;\n\t\t&lt;mediatagname /&gt;\n\t\t&lt;messageaction&gt;&lt;![CDATA[]]&gt;&lt;/messageaction&gt;\n\t\t&lt;messageext&gt;&lt;![CDATA[]]&gt;&lt;/messageext&gt;\n\t\t&lt;emoticongift&gt;\n\t\t\t&lt;packageflag&gt;0&lt;/packageflag&gt;\n\t\t\t&lt;packageid /&gt;\n\t\t&lt;/emoticongift&gt;\n\t\t&lt;emoticonshared&gt;\n\t\t\t&lt;packageflag&gt;0&lt;/packageflag&gt;\n\t\t\t&lt;packageid /&gt;\n\t\t&lt;/emoticonshared&gt;\n\t\t&lt;designershared&gt;\n\t\t\t&lt;designeruin&gt;0&lt;/designeruin&gt;\n\t\t\t&lt;designername&gt;null&lt;/designername&gt;\n\t\t\t&lt;designerrediretcturl&gt;&lt;![CDATA[null]]&gt;&lt;/designerrediretcturl&gt;\n\t\t&lt;/designershared&gt;\n\t\t&lt;emotionpageshared&gt;\n\t\t\t&lt;tid&gt;0&lt;/tid&gt;\n\t\t\t&lt;title&gt;null&lt;/title&gt;\n\t\t\t&lt;desc&gt;null&lt;/desc&gt;\n\t\t\t&lt;iconUrl&gt;&lt;![CDATA[null]]&gt;&lt;/iconUrl&gt;\n\t\t\t&lt;secondUrl&gt;null&lt;/secondUrl&gt;\n\t\t\t&lt;pageType&gt;0&lt;/pageType&gt;\n\t\t\t&lt;setKey&gt;null&lt;/setKey&gt;\n\t\t&lt;/emotionpageshared&gt;\n\t\t&lt;webviewshared&gt;\n\t\t\t&lt;shareUrlOriginal /&gt;\n\t\t\t&lt;shareUrlOpen /&gt;\n\t\t\t&lt;jsAppId /&gt;\n\t\t\t&lt;publisherId /&gt;\n\t\t\t&lt;publisherReqId /&gt;\n\t\t&lt;/webviewshared&gt;\n\t\t&lt;template_id /&gt;\n\t\t&lt;md5 /&gt;\n\t\t&lt;websearch&gt;\n\t\t\t&lt;rec_category&gt;0&lt;/rec_category&gt;\n\t\t\t&lt;channelId&gt;0&lt;/channelId&gt;\n\t\t&lt;/websearch&gt;\n\t\t&lt;weappinfo&gt;\n\t\t\t&lt;username /&gt;\n\t\t\t&lt;appid /&gt;\n\t\t\t&lt;appservicetype&gt;0&lt;/appservicetype&gt;\n\t\t\t&lt;secflagforsinglepagemode&gt;0&lt;/secflagforsinglepagemode&gt;\n\t\t\t&lt;videopageinfo&gt;\n\t\t\t\t&lt;thumbwidth&gt;0&lt;/thumbwidth&gt;\n\t\t\t\t&lt;thumbheight&gt;0&lt;/thumbheight&gt;\n\t\t\t\t&lt;fromopensdk&gt;0&lt;/fromopensdk&gt;\n\t\t\t&lt;/videopageinfo&gt;\n\t\t&lt;/weappinfo&gt;\n\t\t&lt;statextstr /&gt;\n\t\t&lt;musicShareItem&gt;\n\t\t\t&lt;musicDuration&gt;0&lt;/musicDuration&gt;\n\t\t&lt;/musicShareItem&gt;\n\t\t&lt;finderLiveProductShare&gt;\n\t\t\t&lt;finderLiveID&gt;&lt;![CDATA[]]&gt;&lt;/finderLiveID&gt;\n\t\t\t&lt;finderUsername&gt;&lt;![CDATA[]]&gt;&lt;/finderUsername&gt;\n\t\t\t&lt;finderObjectID&gt;&lt;![CDATA[]]&gt;&lt;/finderObjectID&gt;\n\t\t\t&lt;finderNonceID&gt;&lt;![CDATA[]]&gt;&lt;/finderNonceID&gt;\n\t\t\t&lt;liveStatus&gt;&lt;![CDATA[]]&gt;&lt;/liveStatus&gt;\n\t\t\t&lt;appId&gt;&lt;![CDATA[]]&gt;&lt;/appId&gt;\n\t\t\t&lt;pagePath&gt;&lt;![CDATA[]]&gt;&lt;/pagePath&gt;\n\t\t\t&lt;productId&gt;&lt;![CDATA[]]&gt;&lt;/productId&gt;\n\t\t\t&lt;coverUrl&gt;&lt;![CDATA[]]&gt;&lt;/coverUrl&gt;\n\t\t\t&lt;productTitle&gt;&lt;![CDATA[]]&gt;&lt;/productTitle&gt;\n\t\t\t&lt;marketPrice&gt;&lt;![CDATA[0]]&gt;&lt;/marketPrice&gt;\n\t\t\t&lt;sellingPrice&gt;&lt;![CDATA[0]]&gt;&lt;/sellingPrice&gt;\n\t\t\t&lt;platformHeadImg&gt;&lt;![CDATA[]]&gt;&lt;/platformHeadImg&gt;\n\t\t\t&lt;platformName&gt;&lt;![CDATA[]]&gt;&lt;/platformName&gt;\n\t\t\t&lt;shopWindowId&gt;&lt;![CDATA[]]&gt;&lt;/shopWindowId&gt;\n\t\t\t&lt;flashSalePrice&gt;&lt;![CDATA[0]]&gt;&lt;/flashSalePrice&gt;\n\t\t\t&lt;flashSaleEndTime&gt;&lt;![CDATA[0]]&gt;&lt;/flashSaleEndTime&gt;\n\t\t\t&lt;ecSource&gt;&lt;![CDATA[]]&gt;&lt;/ecSource&gt;\n\t\t\t&lt;sellingPriceWording&gt;&lt;![CDATA[]]&gt;&lt;/sellingPriceWording&gt;\n\t\t\t&lt;platformIconURL&gt;&lt;![CDATA[]]&gt;&lt;/platformIconURL&gt;\n\t\t\t&lt;firstProductTagURL&gt;&lt;![CDATA[]]&gt;&lt;/firstProductTagURL&gt;\n\t\t\t&lt;firstProductTagAspectRatioString&gt;&lt;![CDATA[0.0]]&gt;&lt;/firstProductTagAspectRatioString&gt;\n\t\t\t&lt;secondProductTagURL&gt;&lt;![CDATA[]]&gt;&lt;/secondProductTagURL&gt;\n\t\t\t&lt;secondProductTagAspectRatioString&gt;&lt;![CDATA[0.0]]&gt;&lt;/secondProductTagAspectRatioString&gt;\n\t\t\t&lt;firstGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/firstGuaranteeWording&gt;\n\t\t\t&lt;secondGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/secondGuaranteeWording&gt;\n\t\t\t&lt;thirdGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/thirdGuaranteeWording&gt;\n\t\t\t&lt;isPriceBeginShow&gt;false&lt;/isPriceBeginShow&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t\t&lt;promoterKey&gt;&lt;![CDATA[]]&gt;&lt;/promoterKey&gt;\n\t\t\t&lt;discountWording&gt;&lt;![CDATA[]]&gt;&lt;/discountWording&gt;\n\t\t\t&lt;priceSuffixDescription&gt;&lt;![CDATA[]]&gt;&lt;/priceSuffixDescription&gt;\n\t\t\t&lt;productCardKey&gt;&lt;![CDATA[]]&gt;&lt;/productCardKey&gt;\n\t\t\t&lt;isWxShop&gt;&lt;![CDATA[]]&gt;&lt;/isWxShop&gt;\n\t\t\t&lt;brandIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/brandIconUrl&gt;\n\t\t\t&lt;rIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrl&gt;\n\t\t\t&lt;rIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrlDarkMode&gt;\n\t\t\t&lt;topShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrl&gt;\n\t\t\t&lt;topShopIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrlDarkMode&gt;\n\t\t\t&lt;simplifyTopShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrl&gt;\n\t\t\t&lt;simplifyTopShopIconUrlDarkmode&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrlDarkmode&gt;\n\t\t\t&lt;topShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconWidth&gt;\n\t\t\t&lt;topShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconHeight&gt;\n\t\t\t&lt;simplifyTopShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconWidth&gt;\n\t\t\t&lt;simplifyTopShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconHeight&gt;\n\t\t\t&lt;showBoxItemStringList /&gt;\n\t\t&lt;/finderLiveProductShare&gt;\n\t\t&lt;finderOrder&gt;\n\t\t\t&lt;appID&gt;&lt;![CDATA[]]&gt;&lt;/appID&gt;\n\t\t\t&lt;orderID&gt;&lt;![CDATA[]]&gt;&lt;/orderID&gt;\n\t\t\t&lt;path&gt;&lt;![CDATA[]]&gt;&lt;/path&gt;\n\t\t\t&lt;priceWording&gt;&lt;![CDATA[]]&gt;&lt;/priceWording&gt;\n\t\t\t&lt;stateWording&gt;&lt;![CDATA[]]&gt;&lt;/stateWording&gt;\n\t\t\t&lt;productImageURL&gt;&lt;![CDATA[]]&gt;&lt;/productImageURL&gt;\n\t\t\t&lt;products&gt;&lt;![CDATA[]]&gt;&lt;/products&gt;\n\t\t\t&lt;productsCount&gt;&lt;![CDATA[0]]&gt;&lt;/productsCount&gt;\n\t\t\t&lt;orderType&gt;&lt;![CDATA[0]]&gt;&lt;/orderType&gt;\n\t\t\t&lt;newPriceWording&gt;&lt;![CDATA[]]&gt;&lt;/newPriceWording&gt;\n\t\t\t&lt;newStateWording&gt;&lt;![CDATA[]]&gt;&lt;/newStateWording&gt;\n\t\t\t&lt;useNewWording&gt;&lt;![CDATA[0]]&gt;&lt;/useNewWording&gt;\n\t\t&lt;/finderOrder&gt;\n\t\t&lt;finderShopWindowShare&gt;\n\t\t\t&lt;finderUsername&gt;&lt;![CDATA[]]&gt;&lt;/finderUsername&gt;\n\t\t\t&lt;avatar&gt;&lt;![CDATA[]]&gt;&lt;/avatar&gt;\n\t\t\t&lt;nickname&gt;&lt;![CDATA[]]&gt;&lt;/nickname&gt;\n\t\t\t&lt;commodityInStockCount&gt;&lt;![CDATA[]]&gt;&lt;/commodityInStockCount&gt;\n\t\t\t&lt;appId&gt;&lt;![CDATA[]]&gt;&lt;/appId&gt;\n\t\t\t&lt;path&gt;&lt;![CDATA[]]&gt;&lt;/path&gt;\n\t\t\t&lt;appUsername&gt;&lt;![CDATA[]]&gt;&lt;/appUsername&gt;\n\t\t\t&lt;query&gt;&lt;![CDATA[]]&gt;&lt;/query&gt;\n\t\t\t&lt;liteAppId&gt;&lt;![CDATA[]]&gt;&lt;/liteAppId&gt;\n\t\t\t&lt;liteAppPath&gt;&lt;![CDATA[]]&gt;&lt;/liteAppPath&gt;\n\t\t\t&lt;liteAppQuery&gt;&lt;![CDATA[]]&gt;&lt;/liteAppQuery&gt;\n\t\t\t&lt;platformTagURL&gt;&lt;![CDATA[]]&gt;&lt;/platformTagURL&gt;\n\t\t\t&lt;saleWording&gt;&lt;![CDATA[]]&gt;&lt;/saleWording&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t\t&lt;profileTypeWording&gt;&lt;![CDATA[]]&gt;&lt;/profileTypeWording&gt;\n\t\t\t&lt;saleWordingExtra&gt;&lt;![CDATA[]]&gt;&lt;/saleWordingExtra&gt;\n\t\t\t&lt;isWxShop&gt;&lt;![CDATA[]]&gt;&lt;/isWxShop&gt;\n\t\t\t&lt;platformIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/platformIconUrl&gt;\n\t\t\t&lt;brandIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/brandIconUrl&gt;\n\t\t\t&lt;description&gt;&lt;![CDATA[]]&gt;&lt;/description&gt;\n\t\t\t&lt;backgroundUrl&gt;&lt;![CDATA[]]&gt;&lt;/backgroundUrl&gt;\n\t\t\t&lt;darkModePlatformIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/darkModePlatformIconUrl&gt;\n\t\t\t&lt;rIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrl&gt;\n\t\t\t&lt;rIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrlDarkMode&gt;\n\t\t\t&lt;rWords&gt;&lt;![CDATA[]]&gt;&lt;/rWords&gt;\n\t\t\t&lt;topShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrl&gt;\n\t\t\t&lt;topShopIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrlDarkMode&gt;\n\t\t\t&lt;simplifyTopShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrl&gt;\n\t\t\t&lt;simplifyTopShopIconUrlDarkmode&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrlDarkmode&gt;\n\t\t\t&lt;topShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconWidth&gt;\n\t\t\t&lt;topShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconHeight&gt;\n\t\t\t&lt;simplifyTopShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconWidth&gt;\n\t\t\t&lt;simplifyTopShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconHeight&gt;\n\t\t\t&lt;reputationInfo&gt;\n\t\t\t\t&lt;hasReputationInfo&gt;0&lt;/hasReputationInfo&gt;\n\t\t\t\t&lt;reputationScore&gt;0&lt;/reputationScore&gt;\n\t\t\t\t&lt;reputationWording /&gt;\n\t\t\t\t&lt;reputationTextColor /&gt;\n\t\t\t\t&lt;reputationLevelWording /&gt;\n\t\t\t\t&lt;reputationBackgroundColor /&gt;\n\t\t\t&lt;/reputationInfo&gt;\n\t\t\t&lt;productImageURLList /&gt;\n\t\t&lt;/finderShopWindowShare&gt;\n\t\t&lt;findernamecard&gt;\n\t\t\t&lt;username /&gt;\n\t\t\t&lt;avatar&gt;&lt;![CDATA[]]&gt;&lt;/avatar&gt;\n\t\t\t&lt;nickname /&gt;\n\t\t\t&lt;auth_job /&gt;\n\t\t\t&lt;auth_icon&gt;0&lt;/auth_icon&gt;\n\t\t\t&lt;auth_icon_url /&gt;\n\t\t\t&lt;ecSource&gt;&lt;![CDATA[]]&gt;&lt;/ecSource&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t&lt;/findernamecard&gt;\n\t\t&lt;finderGuarantee&gt;\n\t\t\t&lt;scene&gt;&lt;![CDATA[0]]&gt;&lt;/scene&gt;\n\t\t&lt;/finderGuarantee&gt;\n\t\t&lt;directshare&gt;0&lt;/directshare&gt;\n\t\t&lt;gamecenter&gt;\n\t\t\t&lt;namecard&gt;\n\t\t\t\t&lt;iconUrl /&gt;\n\t\t\t\t&lt;name /&gt;\n\t\t\t\t&lt;desc /&gt;\n\t\t\t\t&lt;tail /&gt;\n\t\t\t\t&lt;jumpUrl /&gt;\n\t\t\t\t&lt;liteappId /&gt;\n\t\t\t\t&lt;liteappPath /&gt;\n\t\t\t\t&lt;liteappQuery /&gt;\n\t\t\t\t&lt;liteappMinVersion /&gt;\n\t\t\t&lt;/namecard&gt;\n\t\t&lt;/gamecenter&gt;\n\t\t&lt;patMsg&gt;\n\t\t\t&lt;chatUser /&gt;\n\t\t\t&lt;records&gt;\n\t\t\t\t&lt;recordNum&gt;0&lt;/recordNum&gt;\n\t\t\t&lt;/records&gt;\n\t\t&lt;/patMsg&gt;\n\t\t&lt;secretmsg&gt;\n\t\t\t&lt;issecretmsg&gt;0&lt;/issecretmsg&gt;\n\t\t&lt;/secretmsg&gt;\n\t\t&lt;referfromscene&gt;0&lt;/referfromscene&gt;\n\t\t&lt;gameshare&gt;\n\t\t\t&lt;liteappext&gt;\n\t\t\t\t&lt;liteappbizdata /&gt;\n\t\t\t\t&lt;priority&gt;0&lt;/priority&gt;\n\t\t\t&lt;/liteappext&gt;\n\t\t\t&lt;appbrandext&gt;\n\t\t\t\t&lt;litegameinfo /&gt;\n\t\t\t\t&lt;priority&gt;-1&lt;/priority&gt;\n\t\t\t&lt;/appbrandext&gt;\n\t\t\t&lt;gameshareid /&gt;\n\t\t\t&lt;sharedata /&gt;\n\t\t\t&lt;isvideo&gt;0&lt;/isvideo&gt;\n\t\t\t&lt;duration&gt;-1&lt;/duration&gt;\n\t\t\t&lt;isexposed&gt;0&lt;/isexposed&gt;\n\t\t\t&lt;readtext /&gt;\n\t\t&lt;/gameshare&gt;\n\t\t&lt;tingChatRoomItem&gt;\n\t\t\t&lt;type&gt;0&lt;/type&gt;\n\t\t\t&lt;categoryItem&gt;null&lt;/categoryItem&gt;\n\t\t\t&lt;categoryId /&gt;\n\t\t&lt;/tingChatRoomItem&gt;\n\t\t&lt;mpsharetrace&gt;\n\t\t\t&lt;hasfinderelement&gt;0&lt;/hasfinderelement&gt;\n\t\t\t&lt;lastgmsgid /&gt;\n\t\t&lt;/mpsharetrace&gt;\n\t\t&lt;wxgamecard&gt;\n\t\t\t&lt;framesetname /&gt;\n\t\t\t&lt;mbcarddata /&gt;\n\t\t\t&lt;minpkgversion /&gt;\n\t\t\t&lt;clientextinfo /&gt;\n\t\t\t&lt;mbcardheight&gt;0&lt;/mbcardheight&gt;\n\t\t\t&lt;isoldversion&gt;0&lt;/isoldversion&gt;\n\t\t&lt;/wxgamecard&gt;\n\t\t&lt;ecskfcard&gt;\n\t\t\t&lt;framesetname /&gt;\n\t\t\t&lt;mbcarddata /&gt;\n\t\t\t&lt;minupdateunixtimestamp&gt;0&lt;/minupdateunixtimestamp&gt;\n\t\t\t&lt;needheader&gt;false&lt;/needheader&gt;\n\t\t\t&lt;summary /&gt;\n\t\t&lt;/ecskfcard&gt;\n\t\t&lt;liteapp&gt;\n\t\t\t&lt;id&gt;null&lt;/id&gt;\n\t\t\t&lt;path /&gt;\n\t\t\t&lt;query /&gt;\n\t\t\t&lt;istransparent&gt;0&lt;/istransparent&gt;\n\t\t\t&lt;hideicon&gt;0&lt;/hideicon&gt;\n\t\t\t&lt;forbidforward&gt;0&lt;/forbidforward&gt;\n\t\t&lt;/liteapp&gt;\n\t\t&lt;opensdk_share_is_modified&gt;0&lt;/opensdk_share_is_modified&gt;\n\t&lt;/appmsg&gt;\n\t&lt;fromusername&gt;wxid_ctp9qffuf14b21&lt;/fromusername&gt;\n\t&lt;scene&gt;0&lt;/scene&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;version&gt;1&lt;/version&gt;\n\t\t&lt;appname&gt;&lt;/appname&gt;\n\t&lt;/appinfo&gt;\n\t&lt;commenturl&gt;&lt;/commenturl&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753863953</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_2530z9t0joek22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753863988, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>935d012094e49c13f9f5afd91a4e107f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_i/+JDZDf|v1_MUkCQZjT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5129423232036764184, 'MsgSeq': 871412037}
2025-07-30 16:26:15 | DEBUG | 从群聊消息中提取发送者: wxid_2530z9t0joek22
2025-07-30 16:26:15 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 16:26:15 | INFO | 收到引用消息: 消息ID:2025173132 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 内容:我还没遇到过 引用类型:49
2025-07-30 16:26:15 | INFO | [DouBaoImageToImage] 收到引用消息: 我还没遇到过
2025-07-30 16:26:15 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 16:26:15 | INFO |   - 消息内容: 我还没遇到过
2025-07-30 16:26:15 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-30 16:26:15 | INFO |   - 发送人: wxid_2530z9t0joek22
2025-07-30 16:26:15 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>那是有原因的[捂脸]，出bug一直可以点亮领钻石，你没经历啊？</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg></refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ctp9qffuf14b21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '923018609470436070', 'NewMsgId': '923018609470436070', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': 'Z⁰', 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>935d012094e49c13f9f5afd91a4e107f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_MsCxTCo4|v1_UgscGF+3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753863953', 'SenderWxid': 'wxid_2530z9t0joek22'}
2025-07-30 16:26:15 | INFO |   - 引用消息ID: 
2025-07-30 16:26:15 | INFO |   - 引用消息类型: 
2025-07-30 16:26:15 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>那是有原因的[捂脸]，出bug一直可以点亮领钻石，你没经历啊？</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg></refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ctp9qffuf14b21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 16:26:15 | INFO |   - 引用消息发送人: wxid_2530z9t0joek22
2025-07-30 16:26:34 | DEBUG | 收到消息: {'MsgId': 2013844628, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n02  03年的事'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864007, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_FJzbMjpe|v1_W2uGoPsm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7997562271391733858, 'MsgSeq': 871412038}
2025-07-30 16:26:34 | INFO | 收到文本消息: 消息ID:2013844628 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 @:[] 内容:02  03年的事
2025-07-30 16:26:34 | DEBUG | 处理消息内容: '02  03年的事'
2025-07-30 16:26:34 | DEBUG | 消息内容 '02  03年的事' 不匹配任何命令，忽略
2025-07-30 16:26:57 | DEBUG | 收到消息: {'MsgId': 1694105895, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n这游戏什么时候出来的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864030, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_1YhK6DJ5|v1_8XIRjg1M</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5675375107086015223, 'MsgSeq': 871412039}
2025-07-30 16:26:57 | INFO | 收到文本消息: 消息ID:1694105895 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:这游戏什么时候出来的
2025-07-30 16:26:57 | DEBUG | 处理消息内容: '这游戏什么时候出来的'
2025-07-30 16:26:57 | DEBUG | 消息内容 '这游戏什么时候出来的' 不匹配任何命令，忽略
2025-07-30 16:27:02 | DEBUG | 收到消息: {'MsgId': 521273537, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n没那么早吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864035, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_bJnbrKZu|v1_xvNWOnC7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3835178447599463305, 'MsgSeq': 871412040}
2025-07-30 16:27:02 | INFO | 收到文本消息: 消息ID:521273537 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:没那么早吧
2025-07-30 16:27:02 | DEBUG | 处理消息内容: '没那么早吧'
2025-07-30 16:27:02 | DEBUG | 消息内容 '没那么早吧' 不匹配任何命令，忽略
2025-07-30 16:27:08 | DEBUG | 收到消息: {'MsgId': 659366765, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n应该是22，23吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864041, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_wspOzR+A|v1_xpOgSlIg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 687413391272460329, 'MsgSeq': 871412041}
2025-07-30 16:27:08 | INFO | 收到文本消息: 消息ID:659366765 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:[] 内容:应该是22，23吧
2025-07-30 16:27:08 | DEBUG | 处理消息内容: '应该是22，23吧'
2025-07-30 16:27:08 | DEBUG | 消息内容 '应该是22，23吧' 不匹配任何命令，忽略
2025-07-30 16:27:12 | DEBUG | 收到消息: {'MsgId': 1869522868, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_5kipwrzramxr22:\n<msg><emoji fromusername="wxid_5kipwrzramxr22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="917f700a493e0e4bc30d7f7592bbcb5e" len="1269409" productid="" androidmd5="917f700a493e0e4bc30d7f7592bbcb5e" androidlen="1269409" s60v3md5="917f700a493e0e4bc30d7f7592bbcb5e" s60v3len="1269409" s60v5md5="917f700a493e0e4bc30d7f7592bbcb5e" s60v5len="1269409" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=917f700a493e0e4bc30d7f7592bbcb5e&amp;filekey=30440201010430302e02016e04025348042039313766373030613439336530653462633330643766373539326262636235650203135ea1040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb69100092717a5d6dab50000006e01004fb1534820ce91b1500d04f2e&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=da003382f14ab370fbb1a3356a964745&amp;filekey=30440201010430302e02016e04025348042064613030333338326631346162333730666262316133333536613936343734350203135eb0040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb691000b2e9aa5d6dab50000006e02004fb2534820ce91b1500d04f5a&amp;ef=2&amp;bizid=1022" aeskey="e6218e27599f4c76a354927025d3ebdb" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=00a909f7ccf92ab327a136f29f787dac&amp;filekey=30440201010430302e02016e0402534804203030613930396637636366393261623332376131333666323966373837646163020301a110040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb691000cb2efa5d6dab50000006e03004fb3534820ce91b1500d04f69&amp;ef=3&amp;bizid=1022" externmd5="d1e90465ee834f2adbe5be15cb819fec" width="240" height="235" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864044, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_xVTWxPjY|v1_8blN28Aj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8614897381684971452, 'MsgSeq': 871412042}
2025-07-30 16:27:12 | INFO | 收到表情消息: 消息ID:1869522868 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 MD5:917f700a493e0e4bc30d7f7592bbcb5e 大小:1269409
2025-07-30 16:27:12 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8614897381684971452
2025-07-30 16:27:25 | DEBUG | 收到消息: {'MsgId': 1755469471, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n03我刚出生'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864057, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_2jjS4RhG|v1_xNwWzyQk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1284175009794598386, 'MsgSeq': 871412043}
2025-07-30 16:27:25 | INFO | 收到文本消息: 消息ID:1755469471 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:03我刚出生
2025-07-30 16:27:25 | DEBUG | 处理消息内容: '03我刚出生'
2025-07-30 16:27:25 | DEBUG | 消息内容 '03我刚出生' 不匹配任何命令，忽略
2025-07-30 16:27:29 | DEBUG | 收到消息: {'MsgId': 858957499, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864062, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_tLJOz45C|v1_bzH7xk9Q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 151506209267487585, 'MsgSeq': 871412044}
2025-07-30 16:27:29 | INFO | 收到文本消息: 消息ID:858957499 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:哈哈
2025-07-30 16:27:29 | DEBUG | 处理消息内容: '哈哈'
2025-07-30 16:27:29 | DEBUG | 消息内容 '哈哈' 不匹配任何命令，忽略
2025-07-30 16:27:31 | DEBUG | 收到消息: {'MsgId': 230117432, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n<msg><emoji fromusername="wxid_ugv5ryus4gz622" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="5645dbacdad015932f750e966275094c" len="26751" productid="" androidmd5="5645dbacdad015932f750e966275094c" androidlen="26751" s60v3md5="5645dbacdad015932f750e966275094c" s60v3len="26751" s60v5md5="5645dbacdad015932f750e966275094c" s60v5len="26751" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=5645dbacdad015932f750e966275094c&amp;filekey=3043020101042f302d02016e0402535a042035363435646261636461643031353933326637353065393636323735303934630202687f040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313037303831353137333630303032623065643364336265323162366237393536303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=56eb4725f68d918de678dc88dee1776c&amp;filekey=3043020101042f302d02016e0402535a0420353665623437323566363864393138646536373864633838646565313737366302026880040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313037303831353137333630303033333264373364336265323162366237393536303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey="688e0d6068784515acb06e1eb2833279" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=0457aaaf6334ef82eacf482eece75a3a&amp;filekey=3043020101042f302d02016e0402535a0420303435376161616636333334656638326561636634383265656365373561336102024110040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313037303831353137333630303033383365323364336265323162366237393536303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5="b9346b92ac12066b1d60632936e208ed" width="440" height="410" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864062, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_sUlXY9m2|v1_YJnZE2rP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6633314191477411884, 'MsgSeq': 871412045}
2025-07-30 16:27:31 | INFO | 收到表情消息: 消息ID:230117432 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 MD5:5645dbacdad015932f750e966275094c 大小:26751
2025-07-30 16:27:31 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6633314191477411884
2025-07-30 16:27:45 | DEBUG | 收到消息: {'MsgId': 1363706400, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n宝，暴露年龄了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864078, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_EU2yYq24|v1_cwIuI22U</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2299008670634289419, 'MsgSeq': 871412046}
2025-07-30 16:27:45 | INFO | 收到文本消息: 消息ID:1363706400 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:[] 内容:宝，暴露年龄了
2025-07-30 16:27:45 | DEBUG | 处理消息内容: '宝，暴露年龄了'
2025-07-30 16:27:45 | DEBUG | 消息内容 '宝，暴露年龄了' 不匹配任何命令，忽略
2025-07-30 16:28:08 | DEBUG | 收到消息: {'MsgId': 2019270453, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n抱一丝'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864100, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_YOEqBVWx|v1_YkSQOQNn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 543520063161807124, 'MsgSeq': 871412047}
2025-07-30 16:28:08 | INFO | 收到文本消息: 消息ID:2019270453 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:抱一丝
2025-07-30 16:28:08 | DEBUG | 处理消息内容: '抱一丝'
2025-07-30 16:28:08 | DEBUG | 消息内容 '抱一丝' 不匹配任何命令，忽略
2025-07-30 16:28:10 | DEBUG | 收到消息: {'MsgId': 840679671, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n不过没关系，依然年轻[阴险]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864103, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_UW3eUcs1|v1_CSG/BUQr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6314164885592832837, 'MsgSeq': 871412048}
2025-07-30 16:28:10 | INFO | 收到文本消息: 消息ID:840679671 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:[] 内容:不过没关系，依然年轻[阴险]
2025-07-30 16:28:10 | DEBUG | 处理消息内容: '不过没关系，依然年轻[阴险]'
2025-07-30 16:28:10 | DEBUG | 消息内容 '不过没关系，依然年轻[阴险]' 不匹配任何命令，忽略
2025-07-30 16:28:12 | DEBUG | 收到消息: {'MsgId': 488836824, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864103, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_+xHUMP6d|v1_ekYXqsWf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4267459711940229868, 'MsgSeq': 871412049}
2025-07-30 16:28:12 | INFO | 收到文本消息: 消息ID:488836824 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 @:[] 内容:哈哈哈
2025-07-30 16:28:12 | DEBUG | 处理消息内容: '哈哈哈'
2025-07-30 16:28:12 | DEBUG | 消息内容 '哈哈哈' 不匹配任何命令，忽略
2025-07-30 16:28:34 | DEBUG | 收到消息: {'MsgId': 932767901, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n抱一丝是22  23年。忙糊涂了[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864126, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_vevVQzNm|v1_7b0APtPc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7400350943611690178, 'MsgSeq': 871412050}
2025-07-30 16:28:34 | INFO | 收到文本消息: 消息ID:932767901 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 @:[] 内容:抱一丝是22  23年。忙糊涂了[捂脸]
2025-07-30 16:28:34 | DEBUG | 处理消息内容: '抱一丝是22  23年。忙糊涂了[捂脸]'
2025-07-30 16:28:34 | DEBUG | 消息内容 '抱一丝是22  23年。忙糊涂了[捂脸]' 不匹配任何命令，忽略
2025-07-30 16:28:36 | DEBUG | 收到消息: {'MsgId': 238249762, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n一下激动了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864129, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_MwLEt6Mo|v1_fXpfgL42</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3845023562284896876, 'MsgSeq': 871412051}
2025-07-30 16:28:36 | INFO | 收到文本消息: 消息ID:238249762 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:一下激动了
2025-07-30 16:28:36 | DEBUG | 处理消息内容: '一下激动了'
2025-07-30 16:28:36 | DEBUG | 消息内容 '一下激动了' 不匹配任何命令，忽略
2025-07-30 16:28:51 | DEBUG | 收到消息: {'MsgId': 2142575322, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n[偷笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864144, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_zWjCUyQL|v1_k25DvXno</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5298706564367237315, 'MsgSeq': 871412052}
2025-07-30 16:28:51 | INFO | 收到表情消息: 消息ID:2142575322 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:[偷笑]
2025-07-30 16:28:51 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5298706564367237315
2025-07-30 16:29:40 | DEBUG | 收到消息: {'MsgId': 335189614, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@悦菟ིྀ\u2005我就比你早出生一年'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864193, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_ugv5ryus4gz622]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_OgAb2Ygj|v1_i9h4G6Tv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8554629872156570382, 'MsgSeq': 871412053}
2025-07-30 16:29:40 | INFO | 收到文本消息: 消息ID:335189614 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_ugv5ryus4gz622'] 内容:@悦菟ིྀ 我就比你早出生一年
2025-07-30 16:29:40 | DEBUG | 处理消息内容: '@悦菟ིྀ 我就比你早出生一年'
2025-07-30 16:29:40 | DEBUG | 消息内容 '@悦菟ིྀ 我就比你早出生一年' 不匹配任何命令，忽略
2025-07-30 16:30:22 | DEBUG | 收到消息: {'MsgId': 1680015621, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n年轻真好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864235, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_SYjbM7h3|v1_inYCV+1n</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8195627359946286674, 'MsgSeq': 871412054}
2025-07-30 16:30:22 | INFO | 收到文本消息: 消息ID:1680015621 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:[] 内容:年轻真好
2025-07-30 16:30:22 | DEBUG | 处理消息内容: '年轻真好'
2025-07-30 16:30:22 | DEBUG | 消息内容 '年轻真好' 不匹配任何命令，忽略
2025-07-30 16:30:35 | DEBUG | 收到消息: {'MsgId': 69226535, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_nfjhvkja087s12:\n@枂菟ིྀ\u2005我刷到这个游戏是疫情刚爆发的时候'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864248, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_5kipwrzramxr22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_vSGqurJa|v1_D6FCWYoJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8703981045648860637, 'MsgSeq': 871412055}
2025-07-30 16:30:35 | INFO | 收到文本消息: 消息ID:69226535 来自:27852221909@chatroom 发送人:wxid_nfjhvkja087s12 @:['wxid_5kipwrzramxr22'] 内容:@枂菟ིྀ 我刷到这个游戏是疫情刚爆发的时候
2025-07-30 16:30:35 | DEBUG | 处理消息内容: '@枂菟ིྀ 我刷到这个游戏是疫情刚爆发的时候'
2025-07-30 16:30:35 | DEBUG | 消息内容 '@枂菟ིྀ 我刷到这个游戏是疫情刚爆发的时候' 不匹配任何命令，忽略
2025-07-30 16:30:38 | DEBUG | 收到消息: {'MsgId': 245299701, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_nfjhvkja087s12:\n[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864251, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_xzJq8SVN|v1_EmozD4rY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5405017720474489850, 'MsgSeq': 871412056}
2025-07-30 16:30:38 | INFO | 收到表情消息: 消息ID:245299701 来自:27852221909@chatroom 发送人:wxid_nfjhvkja087s12 @:[] 内容:[捂脸]
2025-07-30 16:30:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5405017720474489850
2025-07-30 16:30:45 | DEBUG | 收到消息: {'MsgId': 1467371123, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_nfjhvkja087s12:\n我估计好多人都是'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864258, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_+2yZFIVn|v1_0zMB//qx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3844453940580387167, 'MsgSeq': 871412057}
2025-07-30 16:30:45 | INFO | 收到文本消息: 消息ID:1467371123 来自:27852221909@chatroom 发送人:wxid_nfjhvkja087s12 @:[] 内容:我估计好多人都是
2025-07-30 16:30:45 | DEBUG | 处理消息内容: '我估计好多人都是'
2025-07-30 16:30:45 | DEBUG | 消息内容 '我估计好多人都是' 不匹配任何命令，忽略
2025-07-30 16:30:47 | DEBUG | 收到消息: {'MsgId': 498985966, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_5kipwrzramxr22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>20年[旺柴]</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8703981045648860637</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_nfjhvkja087s12</chatusr>\n\t\t\t<displayname>欲魅</displayname>\n\t\t\t<content>@枂菟ིྀ\u2005我刷到这个游戏是疫情刚爆发的时候</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;853062869&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;wxid_5kipwrzramxr22&lt;/atuserlist&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_L2DL42Mv|v1_LH2Z3ml7&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753864248</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_5kipwrzramxr22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864260, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>b92137402e0fb43d3330a70a05d04dd3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Yxh17hLA|v1_MmLNkkCH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6033399897246793290, 'MsgSeq': 871412058}
2025-07-30 16:30:47 | DEBUG | 从群聊消息中提取发送者: wxid_5kipwrzramxr22
2025-07-30 16:30:47 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 16:30:47 | INFO | 收到引用消息: 消息ID:498985966 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 内容:20年[旺柴] 引用类型:1
2025-07-30 16:30:47 | INFO | [DouBaoImageToImage] 收到引用消息: 20年[旺柴]
2025-07-30 16:30:47 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 16:30:47 | INFO |   - 消息内容: 20年[旺柴]
2025-07-30 16:30:47 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-30 16:30:47 | INFO |   - 发送人: wxid_5kipwrzramxr22
2025-07-30 16:30:47 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@枂菟ིྀ\u2005我刷到这个游戏是疫情刚爆发的时候', 'Msgid': '8703981045648860637', 'NewMsgId': '8703981045648860637', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '欲魅', 'MsgSource': '<msgsource><sequence_id>853062869</sequence_id>\n\t<atuserlist>wxid_5kipwrzramxr22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_L2DL42Mv|v1_LH2Z3ml7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753864248', 'SenderWxid': 'wxid_5kipwrzramxr22'}
2025-07-30 16:30:47 | INFO |   - 引用消息ID: 
2025-07-30 16:30:47 | INFO |   - 引用消息类型: 
2025-07-30 16:30:47 | INFO |   - 引用消息内容: @枂菟ིྀ 我刷到这个游戏是疫情刚爆发的时候
2025-07-30 16:30:47 | INFO |   - 引用消息发送人: wxid_5kipwrzramxr22
2025-07-30 16:30:48 | DEBUG | 收到消息: {'MsgId': 307056372, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x95sfijdz8xy22:\n现在网页都打不开，是不是倒闭了要我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864260, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_aqTOHtXE|v1_zZMAbJi5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8149110173412569109, 'MsgSeq': 871412059}
2025-07-30 16:30:48 | INFO | 收到文本消息: 消息ID:307056372 来自:27852221909@chatroom 发送人:wxid_x95sfijdz8xy22 @:[] 内容:现在网页都打不开，是不是倒闭了要我
2025-07-30 16:30:48 | DEBUG | 处理消息内容: '现在网页都打不开，是不是倒闭了要我'
2025-07-30 16:30:48 | DEBUG | 消息内容 '现在网页都打不开，是不是倒闭了要我' 不匹配任何命令，忽略
2025-07-30 16:31:01 | DEBUG | 收到消息: {'MsgId': 1185668768, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n这游戏那年开服的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864274, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_G+vBUNJX|v1_nT8e8m50</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7599373046978914269, 'MsgSeq': 871412060}
2025-07-30 16:31:01 | INFO | 收到文本消息: 消息ID:1185668768 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:这游戏那年开服的
2025-07-30 16:31:01 | DEBUG | 处理消息内容: '这游戏那年开服的'
2025-07-30 16:31:01 | DEBUG | 消息内容 '这游戏那年开服的' 不匹配任何命令，忽略
2025-07-30 16:31:04 | DEBUG | 收到消息: {'MsgId': 1942316303, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_5kipwrzramxr22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>我也是[偷笑][偷笑][偷笑]</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8703981045648860637</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_nfjhvkja087s12</chatusr>\n\t\t\t<displayname>欲魅</displayname>\n\t\t\t<content>@枂菟ིྀ\u2005我刷到这个游戏是疫情刚爆发的时候</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;853062869&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;wxid_5kipwrzramxr22&lt;/atuserlist&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_L2DL42Mv|v1_LH2Z3ml7&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753864248</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_5kipwrzramxr22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864276, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>4438e1f8a14261895e708d354931d2cb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Jo4qfD17|v1_KzIV1u88</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1819524834581606489, 'MsgSeq': 871412061}
2025-07-30 16:31:04 | DEBUG | 从群聊消息中提取发送者: wxid_5kipwrzramxr22
2025-07-30 16:31:04 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 16:31:04 | INFO | 收到引用消息: 消息ID:1942316303 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 内容:我也是[偷笑][偷笑][偷笑] 引用类型:1
2025-07-30 16:31:04 | INFO | [DouBaoImageToImage] 收到引用消息: 我也是[偷笑][偷笑][偷笑]
2025-07-30 16:31:04 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 16:31:04 | INFO |   - 消息内容: 我也是[偷笑][偷笑][偷笑]
2025-07-30 16:31:04 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-30 16:31:04 | INFO |   - 发送人: wxid_5kipwrzramxr22
2025-07-30 16:31:04 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@枂菟ིྀ\u2005我刷到这个游戏是疫情刚爆发的时候', 'Msgid': '8703981045648860637', 'NewMsgId': '8703981045648860637', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '欲魅', 'MsgSource': '<msgsource><sequence_id>853062869</sequence_id>\n\t<atuserlist>wxid_5kipwrzramxr22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_L2DL42Mv|v1_LH2Z3ml7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753864248', 'SenderWxid': 'wxid_5kipwrzramxr22'}
2025-07-30 16:31:04 | INFO |   - 引用消息ID: 
2025-07-30 16:31:04 | INFO |   - 引用消息类型: 
2025-07-30 16:31:04 | INFO |   - 引用消息内容: @枂菟ིྀ 我刷到这个游戏是疫情刚爆发的时候
2025-07-30 16:31:04 | INFO |   - 引用消息发送人: wxid_5kipwrzramxr22
2025-07-30 16:31:12 | DEBUG | 收到消息: {'MsgId': 357632810, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n我记得开服就玩了几个月'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864285, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_rvHXWxRx|v1_MzREqDGh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4266432047634921451, 'MsgSeq': 871412062}
2025-07-30 16:31:12 | INFO | 收到文本消息: 消息ID:357632810 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:我记得开服就玩了几个月
2025-07-30 16:31:12 | DEBUG | 处理消息内容: '我记得开服就玩了几个月'
2025-07-30 16:31:12 | DEBUG | 消息内容 '我记得开服就玩了几个月' 不匹配任何命令，忽略
2025-07-30 16:31:16 | DEBUG | 收到消息: {'MsgId': 1493838162, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n好像是19年，7.8月份'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864289, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_iDZNhR4d|v1_WDowkkDp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1809576031476376162, 'MsgSeq': 871412063}
2025-07-30 16:31:16 | INFO | 收到文本消息: 消息ID:1493838162 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:[] 内容:好像是19年，7.8月份
2025-07-30 16:31:16 | DEBUG | 处理消息内容: '好像是19年，7.8月份'
2025-07-30 16:31:16 | DEBUG | 消息内容 '好像是19年，7.8月份' 不匹配任何命令，忽略
2025-07-30 16:31:36 | DEBUG | 收到消息: {'MsgId': 71241, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_nfjhvkja087s12:\n好像是 小羊比我玩早一个来月'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864309, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Gai3b9KE|v1_eSZHPOd9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3410511532239820792, 'MsgSeq': 871412064}
2025-07-30 16:31:36 | INFO | 收到文本消息: 消息ID:71241 来自:27852221909@chatroom 发送人:wxid_nfjhvkja087s12 @:[] 内容:好像是 小羊比我玩早一个来月
2025-07-30 16:31:36 | DEBUG | 处理消息内容: '好像是 小羊比我玩早一个来月'
2025-07-30 16:31:36 | DEBUG | 消息内容 '好像是 小羊比我玩早一个来月' 不匹配任何命令，忽略
2025-07-30 16:32:00 | DEBUG | 收到消息: {'MsgId': 621742410, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n我今天给他号送了一张J'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864332, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_T/JMrdIv|v1_zwR8mAQT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7396517216067528175, 'MsgSeq': 871412065}
2025-07-30 16:32:00 | INFO | 收到文本消息: 消息ID:621742410 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:[] 内容:我今天给他号送了一张J
2025-07-30 16:32:00 | DEBUG | 处理消息内容: '我今天给他号送了一张J'
2025-07-30 16:32:00 | DEBUG | 消息内容 '我今天给他号送了一张J' 不匹配任何命令，忽略
2025-07-30 16:32:04 | DEBUG | 收到消息: {'MsgId': 1029618041, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_5kipwrzramxr22:\n<msg><emoji fromusername="wxid_5kipwrzramxr22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="2038cd0997d58ff893d433761b90c102" len="287064" productid="" androidmd5="2038cd0997d58ff893d433761b90c102" androidlen="287064" s60v3md5="2038cd0997d58ff893d433761b90c102" s60v3len="287064" s60v5md5="2038cd0997d58ff893d433761b90c102" s60v5len="287064" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=2038cd0997d58ff893d433761b90c102&amp;filekey=30350201010421301f020201060402535a04102038cd0997d58ff893d433761b90c1020203046158040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373032343535383030303161313936373938656164373339663537353830393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=077d44cfaa88dc58d90235f154e0f96f&amp;filekey=30350201010421301f020201060402535a0410077d44cfaa88dc58d90235f154e0f96f0203046160040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373032343535383030303835326565373938656164373337323461356630393030303030313036&amp;bizid=1023" aeskey="ffce5aa6251a5d1d505a56004f245db1" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=f913389cb157baaa4a7e3f924aa97f05&amp;filekey=30350201010421301f020201060402535a0410f913389cb157baaa4a7e3f924aa97f050203008920040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373032343535383030306535313661373938656164373366623135356630393030303030313036&amp;bizid=1023" externmd5="44d856e4356de8bf319ccc43144619a7" width="320" height="320" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864336, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_7oq4ejC1|v1_yePWhSYM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1107143683437464443, 'MsgSeq': 871412066}
2025-07-30 16:32:04 | INFO | 收到表情消息: 消息ID:1029618041 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 MD5:2038cd0997d58ff893d433761b90c102 大小:287064
2025-07-30 16:32:04 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1107143683437464443
2025-07-30 16:32:18 | DEBUG | 收到消息: {'MsgId': 848314448, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n我开服就玩了几个月退了，'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864351, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_G7pLjtyL|v1_vHQHfSSQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9034515922393043190, 'MsgSeq': 871412067}
2025-07-30 16:32:18 | INFO | 收到文本消息: 消息ID:848314448 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:我开服就玩了几个月退了，
2025-07-30 16:32:18 | DEBUG | 处理消息内容: '我开服就玩了几个月退了，'
2025-07-30 16:32:18 | DEBUG | 消息内容 '我开服就玩了几个月退了，' 不匹配任何命令，忽略
2025-07-30 16:32:20 | DEBUG | 收到消息: {'MsgId': 76257830, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_nfjhvkja087s12:\n[色][色][色]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864352, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Q4iO2tRz|v1_qIWZfKKM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4710987176630624340, 'MsgSeq': 871412068}
2025-07-30 16:32:20 | INFO | 收到表情消息: 消息ID:76257830 来自:27852221909@chatroom 发送人:wxid_nfjhvkja087s12 @:[] 内容:[色][色][色]
2025-07-30 16:32:20 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4710987176630624340
2025-07-30 16:32:26 | DEBUG | 收到消息: {'MsgId': 541378001, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_nfjhvkja087s12:\n@枂菟ིྀ\u2005三克油我的宝'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864359, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_5kipwrzramxr22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_d+XIWU9O|v1_13wFPI1n</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2586506284428247964, 'MsgSeq': 871412069}
2025-07-30 16:32:26 | INFO | 收到文本消息: 消息ID:541378001 来自:27852221909@chatroom 发送人:wxid_nfjhvkja087s12 @:['wxid_5kipwrzramxr22'] 内容:@枂菟ིྀ 三克油我的宝
2025-07-30 16:32:26 | DEBUG | 处理消息内容: '@枂菟ིྀ 三克油我的宝'
2025-07-30 16:32:26 | DEBUG | 消息内容 '@枂菟ིྀ 三克油我的宝' 不匹配任何命令，忽略
2025-07-30 16:32:46 | DEBUG | 收到消息: {'MsgId': 646692954, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ar7quydkgn7522:\nx 截图 https://github.com/flyhunterl/glucose-buddy '}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864378, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_wJ3kMc95|v1_Vm/7BPPe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ₔ安与²⁰²⁵ : x 截图 https://github.com/flyhunterl/glucose-buddy ', 'NewMsgId': 5236992081670791954, 'MsgSeq': 871412070}
2025-07-30 16:32:46 | INFO | 收到文本消息: 消息ID:646692954 来自:47325400669@chatroom 发送人:wxid_ar7quydkgn7522 @:[] 内容:x 截图 https://github.com/flyhunterl/glucose-buddy 
2025-07-30 16:32:46 | DEBUG | 处理消息内容: 'x 截图 https://github.com/flyhunterl/glucose-buddy'
2025-07-30 16:32:46 | DEBUG | 消息内容 'x 截图 https://github.com/flyhunterl/glucose-buddy' 不匹配任何命令，忽略
2025-07-30 16:32:56 | DEBUG | 收到消息: {'MsgId': 407917952, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_z3wc4zex3vr822:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="7262787471696971776c6e6c7a787a63" encryver="0" cdnthumbaeskey="7262787471696971776c6e6c7a787a63" cdnthumburl="3057020100044b304902010002046efce8ee02033d14ba02043304b1a302046889d7a3042462343863633161362d613063322d343861632d393138642d6634633531633561363730620204052828010201000405004c4dfe00ef8ae73c" cdnthumblength="2825" cdnthumbheight="75" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046efce8ee02033d14ba02043304b1a302046889d7a3042462343863633161362d613063322d343861632d393138642d6634633531633561363730620204052828010201000405004c4dfe00ef8ae73c" length="53888" cdnbigimgurl="3057020100044b304902010002046efce8ee02033d14ba02043304b1a302046889d7a3042462343863633161362d613063322d343861632d393138642d6634633531633561363730620204052828010201000405004c4dfe00ef8ae73c" hdlength="202791" md5="71a8cd0e9c3e325b842ebca2158e08ae">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864388, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>cb84fc632efdac1bf09bb527945a175a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_9xr+GQJ5|v1_0WNQKvtN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小艾上午在群聊中发了一张图片', 'NewMsgId': 2720618326042128578, 'MsgSeq': 871412071}
2025-07-30 16:32:56 | INFO | 收到图片消息: 消息ID:407917952 来自:47325400669@chatroom 发送人:wxid_z3wc4zex3vr822 XML:<?xml version="1.0"?><msg><img aeskey="7262787471696971776c6e6c7a787a63" encryver="0" cdnthumbaeskey="7262787471696971776c6e6c7a787a63" cdnthumburl="3057020100044b304902010002046efce8ee02033d14ba02043304b1a302046889d7a3042462343863633161362d613063322d343861632d393138642d6634633531633561363730620204052828010201000405004c4dfe00ef8ae73c" cdnthumblength="2825" cdnthumbheight="75" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046efce8ee02033d14ba02043304b1a302046889d7a3042462343863633161362d613063322d343861632d393138642d6634633531633561363730620204052828010201000405004c4dfe00ef8ae73c" length="53888" cdnbigimgurl="3057020100044b304902010002046efce8ee02033d14ba02043304b1a302046889d7a3042462343863633161362d613063322d343861632d393138642d6634633531633561363730620204052828010201000405004c4dfe00ef8ae73c" hdlength="202791" md5="71a8cd0e9c3e325b842ebca2158e08ae"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 16:32:56 | INFO | [ImageEcho] 保存图片信息成功，当前群 47325400669@chatroom 已存储 5 张图片
2025-07-30 16:32:56 | INFO | [TimerTask] 缓存图片消息: 407917952
2025-07-30 16:36:04 | DEBUG | 收到消息: {'MsgId': 1095922674, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n哈哈哈，客气了，多了也没用呀[呲牙]@欲魅\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753864577, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_nfjhvkja087s12</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Y+1METgz|v1_SVqqqYUf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4221822469314230416, 'MsgSeq': 871412072}
2025-07-30 16:36:04 | INFO | 收到文本消息: 消息ID:1095922674 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:['wxid_nfjhvkja087s12'] 内容:哈哈哈，客气了，多了也没用呀[呲牙]@欲魅 
2025-07-30 16:36:04 | DEBUG | 处理消息内容: '哈哈哈，客气了，多了也没用呀[呲牙]@欲魅'
2025-07-30 16:36:04 | DEBUG | 消息内容 '哈哈哈，客气了，多了也没用呀[呲牙]@欲魅' 不匹配任何命令，忽略
2025-07-30 16:41:54 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-30 16:47:15 | DEBUG | 收到消息: {'MsgId': 1839392295, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="d354fb0c5bc14c2e4d50df2645ac240c" encryver="1" cdnthumbaeskey="d354fb0c5bc14c2e4d50df2645ac240c" cdnthumburl="3057020100044b304902010002049363814102032f5149020475328e7102046889dc1f042465633234623362332d356562392d343230352d623838322d663839303830363237303430020405250a020201000405004c543d00" cdnthumblength="8804" cdnthumbheight="150" cdnthumbwidth="67" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f5149020475328e7102046889dc1f042465633234623362332d356562392d343230352d623838322d663839303830363237303430020405250a020201000405004c543d00" length="117670" md5="2a618b22f31f624d156e7af4ef89af95" originsourcemd5="8481cdcd6377998f3bbd887179eccc07">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865248, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>190e023c5e2ae4f81f9a09a2faf581ba_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ZsHHh1Cs|v1_c9/kykOM</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 2803076792099602220, 'MsgSeq': 871412073}
2025-07-30 16:47:15 | INFO | 收到图片消息: 消息ID:1839392295 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="d354fb0c5bc14c2e4d50df2645ac240c" encryver="1" cdnthumbaeskey="d354fb0c5bc14c2e4d50df2645ac240c" cdnthumburl="3057020100044b304902010002049363814102032f5149020475328e7102046889dc1f042465633234623362332d356562392d343230352d623838322d663839303830363237303430020405250a020201000405004c543d00" cdnthumblength="8804" cdnthumbheight="150" cdnthumbwidth="67" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f5149020475328e7102046889dc1f042465633234623362332d356562392d343230352d623838322d663839303830363237303430020405250a020201000405004c543d00" length="117670" md5="2a618b22f31f624d156e7af4ef89af95" originsourcemd5="8481cdcd6377998f3bbd887179eccc07"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 16:47:16 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-30 16:47:16 | INFO | [TimerTask] 缓存图片消息: 1839392295
2025-07-30 16:48:21 | DEBUG | 收到消息: {'MsgId': 604927266, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="043053c2ac96809a5cfad6f3a7843aa7" encryver="1" cdnthumbaeskey="043053c2ac96809a5cfad6f3a7843aa7" cdnthumburl="3057020100044b30490201000204e65f4f9102032f5e2d0204f6bde17c02046889ccce042464623965643265362d396635322d346235322d383431632d3565343039386264626138620204052418020201000405004c4d9900" cdnthumblength="13693" cdnthumbheight="240" cdnthumbwidth="240" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204e65f4f9102032f5e2d0204f6bde17c02046889ccce042464623965643265362d396635322d346235322d383431632d3565343039386264626138620204052418020201000405004c4d9900" length="55827" md5="c1d4fa1587bfdc5f87f234df128813d4" originsourcemd5="797b868381767f2c1c6a3020301fa9e2">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865314, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>e251d74abc61d40e7bc45eb344239b74_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_NCvWdEiM|v1_6ggcFOBD</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 515127048091849105, 'MsgSeq': 871412074}
2025-07-30 16:48:21 | INFO | 收到图片消息: 消息ID:604927266 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="043053c2ac96809a5cfad6f3a7843aa7" encryver="1" cdnthumbaeskey="043053c2ac96809a5cfad6f3a7843aa7" cdnthumburl="3057020100044b30490201000204e65f4f9102032f5e2d0204f6bde17c02046889ccce042464623965643265362d396635322d346235322d383431632d3565343039386264626138620204052418020201000405004c4d9900" cdnthumblength="13693" cdnthumbheight="240" cdnthumbwidth="240" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204e65f4f9102032f5e2d0204f6bde17c02046889ccce042464623965643265362d396635322d346235322d383431632d3565343039386264626138620204052418020201000405004c4d9900" length="55827" md5="c1d4fa1587bfdc5f87f234df128813d4" originsourcemd5="797b868381767f2c1c6a3020301fa9e2"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 16:48:22 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-30 16:48:22 | INFO | [TimerTask] 缓存图片消息: 604927266
2025-07-30 16:49:43 | DEBUG | 收到消息: {'MsgId': 1493777668, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="9e953c4ce3483d24cf645f7de5f15001" encryver="1" cdnthumbaeskey="9e953c4ce3483d24cf645f7de5f15001" cdnthumburl="3057020100044b30490201000204a406ecdc02032dcdc902042de8d37402046889bae1042462343234633432372d653262382d346662332d383135662d666361656534663438393837020405290a020201000405004c537500" cdnthumblength="3949" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204a406ecdc02032dcdc902042de8d37402046889bae1042462343234633432372d653262382d346662332d383135662d666361656534663438393837020405290a020201000405004c537500" length="27750" md5="d9a923f765fde582d1b35b7c53bf69fd" originsourcemd5="5c642f2cb16429705d10895aa3d0d5ad">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865396, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>01af780762a1b556d9e8817b7e4ab1e2_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_fBcMbKxB|v1_Y9JiweHY</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 5563908689620694767, 'MsgSeq': 871412075}
2025-07-30 16:49:43 | INFO | 收到图片消息: 消息ID:1493777668 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="9e953c4ce3483d24cf645f7de5f15001" encryver="1" cdnthumbaeskey="9e953c4ce3483d24cf645f7de5f15001" cdnthumburl="3057020100044b30490201000204a406ecdc02032dcdc902042de8d37402046889bae1042462343234633432372d653262382d346662332d383135662d666361656534663438393837020405290a020201000405004c537500" cdnthumblength="3949" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204a406ecdc02032dcdc902042de8d37402046889bae1042462343234633432372d653262382d346662332d383135662d666361656534663438393837020405290a020201000405004c537500" length="27750" md5="d9a923f765fde582d1b35b7c53bf69fd" originsourcemd5="5c642f2cb16429705d10895aa3d0d5ad"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 16:49:44 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-30 16:49:44 | INFO | [TimerTask] 缓存图片消息: 1493777668
2025-07-30 16:49:58 | DEBUG | 收到消息: {'MsgId': 132830403, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg>\n\t\t<title>晴天的聊天记录</title>\n\t\t<des>晴天: #跳蛋 #直播 #流量密码 又舒服上了\n晴天: [视频]\n晴天: [视频]\n晴天: [视频]\n晴天: [视频]...</des>\n\t\t<action>view</action>\n\t\t<type>19</type>\n\t\t<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>\n\t\t<recorditem><![CDATA[<recordinfo><title>晴天的聊天记录</title><desc>晴天:&#x20;#跳蛋&#x20;#直播&#x20;#流量密码&#x20;又舒服上了&#x0A;晴天:&#x20;[视频]&#x0A;晴天:&#x20;[视频]&#x0A;晴天:&#x20;[视频]&#x0A;晴天:&#x20;[视频]...</desc><datalist count="6"><dataitem datatype="1" dataid="7a905f41e9a3844108c178f5d6867e51"><datadesc>#跳蛋&#x20;#直播&#x20;#流量密码&#x20;又舒服上了</datadesc><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><fromnewmsgid>8964142133043948351</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="ef026d003605ba1d02b7cafe5c20d782"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc6042462306433633430312d333035342d343535382d383234312d3539346165326538353763390204059820010201000405004c4d3700</cdnthumburl><cdnthumbkey>44d5db26b8828a1463047bb6e7007f5c</cdnthumbkey><thumbfullmd5>15b21815d6e10015c0d42797252f9a88</thumbfullmd5><thumbsize>16491</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc3042431366664613764352d333637392d346561652d386438302d6534363661613262626230350204051400040201000405004c4dfd00</cdndataurl><cdndatakey>5d6930264838d6c2da0e645b34663ec7</cdndatakey><fullmd5>699e19c7409fa48361de2c04c374c8ef</fullmd5><datasize>318717</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>6</duration><head256md5>e001329e6a7c6e4b44c3afa5fd6a78f9</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>61fb737e040a64fc5feb57a5695f3476_</messageuuid><fromnewmsgid>7522386459543257895</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="a78214d7ce6d57efd5be1338450935f6"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc6042462366161386132362d613231622d346661652d383430662d3963363734373863313331360204059820010201000405004c57c300</cdnthumburl><cdnthumbkey>b35112a4e5b52c6d9e2f1fa42490da9a</cdnthumbkey><thumbfullmd5>c74b0f485bdf198fcde80c6f1128ffc9</thumbfullmd5><thumbsize>18361</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc3042465366665633732372d633833332d346131392d613434342d3464663862336330373036660204051400040201000405004c53d900</cdndataurl><cdndatakey>64cf67d58749c16b1d8b59cede4c85ef</cdndatakey><fullmd5>bc1d4d2d5c7bd5c170c1830f134480b1</fullmd5><datasize>422108</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>6</duration><head256md5>2192b0737162579d1ecc6379395ae699</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>ee24fecfa3c5f7cd264f33557f121d7d_</messageuuid><fromnewmsgid>4466749518811615093</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="73cbc73fb2aaa4d8f0838e46d7bd4b06"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc6042436343832303033642d383931372d343232352d383661652d3936326365363132633838610204059420010201000405004c51e700</cdnthumburl><cdnthumbkey>a07274813c2328f5b3d8b45d9c6c21d5</cdnthumbkey><thumbfullmd5>16f3b24f21cd547452db0ab308aa20dc</thumbfullmd5><thumbsize>16477</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc2042436646264316238632d346263612d346531392d616161382d3938396537626465663265610204051400040201000405004c511d00</cdndataurl><cdndatakey>385e26ace826f72ce6b7a95a8e5b9724</cdndatakey><fullmd5>87bbf962c9f085103426a788c456c66f</fullmd5><datasize>379684</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>7</duration><head256md5>ede5bd5794743f4ab5d53983d7578b34</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>a7dc23320c8e32ffaca00d07756a2ddf_</messageuuid><fromnewmsgid>3085832214441986046</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="c5b2a923766f17d9ab74b4c0192958a4"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc7042466623136623039652d663632392d343963362d613234622d3761353734313735656539660204059420010201000405004c4dff00</cdnthumburl><cdnthumbkey>2e8774cff2d8c4341533f5d12be96c22</cdnthumbkey><thumbfullmd5>df9f1e6d2e8c76096d7d6d6d99fd432c</thumbfullmd5><thumbsize>17173</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc3042437636165656532642d326231352d343531322d386535642d3335303530343434363439610204051800040201000405004c54a100</cdndataurl><cdndatakey>1279289b4bee4d0086aa77c04a187feb</cdndatakey><fullmd5>56d140130e21dc9dcfb23e46d8fa9c8c</fullmd5><datasize>479331</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>7</duration><head256md5>dfbcb3b5761b5d65646e8c72a547f4a5</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>85791f6f3d7c0984eb935ef5680bda67_</messageuuid><fromnewmsgid>309084258776682463</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="364a5d827a6bf2071526c327475d8ee0"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc7042464333863633230382d343761332d343262382d623132382d3330343164373332383533610204059820010201000405004c54a300</cdnthumburl><cdnthumbkey>bca896a9a95c7e769a667cce791b20a2</cdnthumbkey><thumbfullmd5>86836e367eda5cde5e408e30228532f8</thumbfullmd5><thumbsize>14014</thumbsize><cdndataurl>3057020100044b30490201000204500fecb502032f5a4502040d9b3cb702046889a5ef042464666466333538662d623062382d343938342d383935612d3631346331633433343630330204051400040201000405004c550500</cdndataurl><cdndatakey>964b3097c64c613135f89fccb4433eb9</cdndatakey><fullmd5>d1836b5ebad7f9a7af16898ab5a3c453</fullmd5><datasize>292234</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>6</duration><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>9096249cef4d81f794095f834b6444e0_</messageuuid><fromnewmsgid>612870437620571636</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem></datalist><favcreatetime>1753865411237</favcreatetime></recordinfo>]]></recorditem>\n\t\t<appattach />\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865411, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<passthrough>\n\t\t<forward_depth>1</forward_depth>\n\t</passthrough>\n\t<sec_msg_node>\n\t\t<uuid>ca34a8d0f3e284359cd25ee4a1b6da68_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_zJHDoWCr|v1_WxI8xAmY</signature>\n</msgsource>\n', 'PushContent': '小爱 : [聊天记录]', 'NewMsgId': 8707265050634462041, 'MsgSeq': 871412076}
2025-07-30 16:49:58 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-30 16:49:58 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg>
		<title>晴天的聊天记录</title>
		<des>晴天: #跳蛋 #直播 #流量密码 又舒服上了
晴天: [视频]
晴天: [视频]
晴天: [视频]
晴天: [视频]...</des>
		<action>view</action>
		<type>19</type>
		<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>
		<recorditem><![CDATA[<recordinfo><title>晴天的聊天记录</title><desc>晴天:&#x20;#跳蛋&#x20;#直播&#x20;#流量密码&#x20;又舒服上了&#x0A;晴天:&#x20;[视频]&#x0A;晴天:&#x20;[视频]&#x0A;晴天:&#x20;[视频]&#x0A;晴天:&#x20;[视频]...</desc><datalist count="6"><dataitem datatype="1" dataid="7a905f41e9a3844108c178f5d6867e51"><datadesc>#跳蛋&#x20;#直播&#x20;#流量密码&#x20;又舒服上了</datadesc><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><fromnewmsgid>8964142133043948351</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="ef026d003605ba1d02b7cafe5c20d782"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc6042462306433633430312d333035342d343535382d383234312d3539346165326538353763390204059820010201000405004c4d3700</cdnthumburl><cdnthumbkey>44d5db26b8828a1463047bb6e7007f5c</cdnthumbkey><thumbfullmd5>15b21815d6e10015c0d42797252f9a88</thumbfullmd5><thumbsize>16491</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc3042431366664613764352d333637392d346561652d386438302d6534363661613262626230350204051400040201000405004c4dfd00</cdndataurl><cdndatakey>5d6930264838d6c2da0e645b34663ec7</cdndatakey><fullmd5>699e19c7409fa48361de2c04c374c8ef</fullmd5><datasize>318717</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>6</duration><head256md5>e001329e6a7c6e4b44c3afa5fd6a78f9</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>61fb737e040a64fc5feb57a5695f3476_</messageuuid><fromnewmsgid>7522386459543257895</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="a78214d7ce6d57efd5be1338450935f6"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc6042462366161386132362d613231622d346661652d383430662d3963363734373863313331360204059820010201000405004c57c300</cdnthumburl><cdnthumbkey>b35112a4e5b52c6d9e2f1fa42490da9a</cdnthumbkey><thumbfullmd5>c74b0f485bdf198fcde80c6f1128ffc9</thumbfullmd5><thumbsize>18361</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc3042465366665633732372d633833332d346131392d613434342d3464663862336330373036660204051400040201000405004c53d900</cdndataurl><cdndatakey>64cf67d58749c16b1d8b59cede4c85ef</cdndatakey><fullmd5>bc1d4d2d5c7bd5c170c1830f134480b1</fullmd5><datasize>422108</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>6</duration><head256md5>2192b0737162579d1ecc6379395ae699</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>ee24fecfa3c5f7cd264f33557f121d7d_</messageuuid><fromnewmsgid>4466749518811615093</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="73cbc73fb2aaa4d8f0838e46d7bd4b06"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc6042436343832303033642d383931372d343232352d383661652d3936326365363132633838610204059420010201000405004c51e700</cdnthumburl><cdnthumbkey>a07274813c2328f5b3d8b45d9c6c21d5</cdnthumbkey><thumbfullmd5>16f3b24f21cd547452db0ab308aa20dc</thumbfullmd5><thumbsize>16477</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc2042436646264316238632d346263612d346531392d616161382d3938396537626465663265610204051400040201000405004c511d00</cdndataurl><cdndatakey>385e26ace826f72ce6b7a95a8e5b9724</cdndatakey><fullmd5>87bbf962c9f085103426a788c456c66f</fullmd5><datasize>379684</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>7</duration><head256md5>ede5bd5794743f4ab5d53983d7578b34</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>a7dc23320c8e32ffaca00d07756a2ddf_</messageuuid><fromnewmsgid>3085832214441986046</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="c5b2a923766f17d9ab74b4c0192958a4"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc7042466623136623039652d663632392d343963362d613234622d3761353734313735656539660204059420010201000405004c4dff00</cdnthumburl><cdnthumbkey>2e8774cff2d8c4341533f5d12be96c22</cdnthumbkey><thumbfullmd5>df9f1e6d2e8c76096d7d6d6d99fd432c</thumbfullmd5><thumbsize>17173</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc3042437636165656532642d326231352d343531322d386535642d3335303530343434363439610204051800040201000405004c54a100</cdndataurl><cdndatakey>1279289b4bee4d0086aa77c04a187feb</cdndatakey><fullmd5>56d140130e21dc9dcfb23e46d8fa9c8c</fullmd5><datasize>479331</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>7</duration><head256md5>dfbcb3b5761b5d65646e8c72a547f4a5</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>85791f6f3d7c0984eb935ef5680bda67_</messageuuid><fromnewmsgid>309084258776682463</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="364a5d827a6bf2071526c327475d8ee0"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc7042464333863633230382d343761332d343262382d623132382d3330343164373332383533610204059820010201000405004c54a300</cdnthumburl><cdnthumbkey>bca896a9a95c7e769a667cce791b20a2</cdnthumbkey><thumbfullmd5>86836e367eda5cde5e408e30228532f8</thumbfullmd5><thumbsize>14014</thumbsize><cdndataurl>3057020100044b30490201000204500fecb502032f5a4502040d9b3cb702046889a5ef042464666466333538662d623062382d343938342d383935612d3631346331633433343630330204051400040201000405004c550500</cdndataurl><cdndatakey>964b3097c64c613135f89fccb4433eb9</cdndatakey><fullmd5>d1836b5ebad7f9a7af16898ab5a3c453</fullmd5><datasize>292234</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>6</duration><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>9096249cef4d81f794095f834b6444e0_</messageuuid><fromnewmsgid>612870437620571636</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem></datalist><favcreatetime>1753865411237</favcreatetime></recordinfo>]]></recorditem>
		<appattach />
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 16:49:58 | DEBUG | XML消息类型: 19
2025-07-30 16:49:58 | DEBUG | XML消息标题: 晴天的聊天记录
2025-07-30 16:49:58 | DEBUG | XML消息描述: 晴天: #跳蛋 #直播 #流量密码 又舒服上了
晴天: [视频]
晴天: [视频]
晴天: [视频]
晴天: [视频]...
2025-07-30 16:49:58 | DEBUG | XML消息URL: https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&from=singlemessage&isappinstalled=0
2025-07-30 16:49:58 | INFO | 未知的XML消息类型: 19
2025-07-30 16:49:58 | INFO | 消息标题: 晴天的聊天记录
2025-07-30 16:49:58 | INFO | 消息描述: 晴天: #跳蛋 #直播 #流量密码 又舒服上了
晴天: [视频]
晴天: [视频]
晴天: [视频]
晴天: [视频]...
2025-07-30 16:49:58 | INFO | 消息URL: https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&from=singlemessage&isappinstalled=0
2025-07-30 16:49:58 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg>
		<title>晴天的聊天记录</title>
		<des>晴天: #跳蛋 #直播 #流量密码 又舒服上了
晴天: [视频]
晴天: [视频]
晴天: [视频]
晴天: [视频]...</des>
		<action>view</action>
		<type>19</type>
		<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>
		<recorditem><![CDATA[<recordinfo><title>晴天的聊天记录</title><desc>晴天:&#x20;#跳蛋&#x20;#直播&#x20;#流量密码&#x20;又舒服上了&#x0A;晴天:&#x20;[视频]&#x0A;晴天:&#x20;[视频]&#x0A;晴天:&#x20;[视频]&#x0A;晴天:&#x20;[视频]...</desc><datalist count="6"><dataitem datatype="1" dataid="7a905f41e9a3844108c178f5d6867e51"><datadesc>#跳蛋&#x20;#直播&#x20;#流量密码&#x20;又舒服上了</datadesc><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><fromnewmsgid>8964142133043948351</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="ef026d003605ba1d02b7cafe5c20d782"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc6042462306433633430312d333035342d343535382d383234312d3539346165326538353763390204059820010201000405004c4d3700</cdnthumburl><cdnthumbkey>44d5db26b8828a1463047bb6e7007f5c</cdnthumbkey><thumbfullmd5>15b21815d6e10015c0d42797252f9a88</thumbfullmd5><thumbsize>16491</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc3042431366664613764352d333637392d346561652d386438302d6534363661613262626230350204051400040201000405004c4dfd00</cdndataurl><cdndatakey>5d6930264838d6c2da0e645b34663ec7</cdndatakey><fullmd5>699e19c7409fa48361de2c04c374c8ef</fullmd5><datasize>318717</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>6</duration><head256md5>e001329e6a7c6e4b44c3afa5fd6a78f9</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>61fb737e040a64fc5feb57a5695f3476_</messageuuid><fromnewmsgid>7522386459543257895</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="a78214d7ce6d57efd5be1338450935f6"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc6042462366161386132362d613231622d346661652d383430662d3963363734373863313331360204059820010201000405004c57c300</cdnthumburl><cdnthumbkey>b35112a4e5b52c6d9e2f1fa42490da9a</cdnthumbkey><thumbfullmd5>c74b0f485bdf198fcde80c6f1128ffc9</thumbfullmd5><thumbsize>18361</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc3042465366665633732372d633833332d346131392d613434342d3464663862336330373036660204051400040201000405004c53d900</cdndataurl><cdndatakey>64cf67d58749c16b1d8b59cede4c85ef</cdndatakey><fullmd5>bc1d4d2d5c7bd5c170c1830f134480b1</fullmd5><datasize>422108</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>6</duration><head256md5>2192b0737162579d1ecc6379395ae699</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>ee24fecfa3c5f7cd264f33557f121d7d_</messageuuid><fromnewmsgid>4466749518811615093</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="73cbc73fb2aaa4d8f0838e46d7bd4b06"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc6042436343832303033642d383931372d343232352d383661652d3936326365363132633838610204059420010201000405004c51e700</cdnthumburl><cdnthumbkey>a07274813c2328f5b3d8b45d9c6c21d5</cdnthumbkey><thumbfullmd5>16f3b24f21cd547452db0ab308aa20dc</thumbfullmd5><thumbsize>16477</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc2042436646264316238632d346263612d346531392d616161382d3938396537626465663265610204051400040201000405004c511d00</cdndataurl><cdndatakey>385e26ace826f72ce6b7a95a8e5b9724</cdndatakey><fullmd5>87bbf962c9f085103426a788c456c66f</fullmd5><datasize>379684</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>7</duration><head256md5>ede5bd5794743f4ab5d53983d7578b34</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>a7dc23320c8e32ffaca00d07756a2ddf_</messageuuid><fromnewmsgid>3085832214441986046</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="c5b2a923766f17d9ab74b4c0192958a4"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc7042466623136623039652d663632392d343963362d613234622d3761353734313735656539660204059420010201000405004c4dff00</cdnthumburl><cdnthumbkey>2e8774cff2d8c4341533f5d12be96c22</cdnthumbkey><thumbfullmd5>df9f1e6d2e8c76096d7d6d6d99fd432c</thumbfullmd5><thumbsize>17173</thumbsize><cdndataurl>3057020100044b304902010002049363814102032f51490204df31227502046889dcc3042437636165656532642d326231352d343531322d386535642d3335303530343434363439610204051800040201000405004c54a100</cdndataurl><cdndatakey>1279289b4bee4d0086aa77c04a187feb</cdndatakey><fullmd5>56d140130e21dc9dcfb23e46d8fa9c8c</fullmd5><datasize>479331</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>7</duration><head256md5>dfbcb3b5761b5d65646e8c72a547f4a5</head256md5><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>85791f6f3d7c0984eb935ef5680bda67_</messageuuid><fromnewmsgid>309084258776682463</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem><dataitem datatype="4" dataid="364a5d827a6bf2071526c327475d8ee0"><cdnthumburl>3057020100044b3049020100020419baeb5502032f9f0702045a44306f02046888afc7042464333863633230382d343761332d343262382d623132382d3330343164373332383533610204059820010201000405004c54a300</cdnthumburl><cdnthumbkey>bca896a9a95c7e769a667cce791b20a2</cdnthumbkey><thumbfullmd5>86836e367eda5cde5e408e30228532f8</thumbfullmd5><thumbsize>14014</thumbsize><cdndataurl>3057020100044b30490201000204500fecb502032f5a4502040d9b3cb702046889a5ef042464666466333538662d623062382d343938342d383935612d3631346331633433343630330204051400040201000405004c550500</cdndataurl><cdndatakey>964b3097c64c613135f89fccb4433eb9</cdndatakey><fullmd5>d1836b5ebad7f9a7af16898ab5a3c453</fullmd5><datasize>292234</datasize><datafmt>mp4</datafmt><thumbwidth>295</thumbwidth><thumbheight>540</thumbheight><duration>6</duration><sourcename>晴天</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/UpORFS6QwlfG2Oanezhbh4hZ2ic0H4PQxaQnbH83dMQnZG9oGf0k1kXc2NQot08ia3t36mlTUrj9aYo8fjStbK3micgZQrwefRRaKjhDEfkY8ibGhIXzBf9Irc9lib3kvw0VcH4ruMd3anLWUbgpUTOdfnQ/132</sourceheadurl><sourcetime>2025-7-29&#x20;19:17</sourcetime><srcMsgCreateTime>1753787820</srcMsgCreateTime><messageuuid>9096249cef4d81f794095f834b6444e0_</messageuuid><fromnewmsgid>612870437620571636</fromnewmsgid><dataitemsource><hashusername>6552a6fcbe7ab1b21cc625cb6d639ae7990c78982990dafca747aee108e3dce6</hashusername></dataitemsource></dataitem></datalist><favcreatetime>1753865411237</favcreatetime></recordinfo>]]></recorditem>
		<appattach />
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 16:50:30 | DEBUG | 收到消息: {'MsgId': 1430715821, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="891be8c43a157d90a2f0e610f8212ad1" encryver="1" cdnthumbaeskey="891be8c43a157d90a2f0e610f8212ad1" cdnthumburl="3057020100044b304902010002049363814102032f51490204df31227502046889dce2042439356335393535662d353365312d343566622d393330392d653862363633336337643962020405250a020201000405004c51e500" cdnthumblength="12056" cdnthumbheight="240" cdnthumbwidth="135" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204df31227502046889dce2042439356335393535662d353365312d343566622d393330392d653862363633336337643962020405250a020201000405004c51e500" length="74298" md5="7564b9752074ffd5d2d889185e951e45" originsourcemd5="c9d2f1a74bca60c6fd45aa3f82550955">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865442, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>1a6e31a63d6a1fbd8a062471227008f6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_oVLeUCGd|v1_EQj0Y9wE</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 9151256789340054627, 'MsgSeq': 871412077}
2025-07-30 16:50:30 | INFO | 收到图片消息: 消息ID:1430715821 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="891be8c43a157d90a2f0e610f8212ad1" encryver="1" cdnthumbaeskey="891be8c43a157d90a2f0e610f8212ad1" cdnthumburl="3057020100044b304902010002049363814102032f51490204df31227502046889dce2042439356335393535662d353365312d343566622d393330392d653862363633336337643962020405250a020201000405004c51e500" cdnthumblength="12056" cdnthumbheight="240" cdnthumbwidth="135" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204df31227502046889dce2042439356335393535662d353365312d343566622d393330392d653862363633336337643962020405250a020201000405004c51e500" length="74298" md5="7564b9752074ffd5d2d889185e951e45" originsourcemd5="c9d2f1a74bca60c6fd45aa3f82550955"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 16:50:30 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-30 16:50:30 | INFO | [TimerTask] 缓存图片消息: 1430715821
2025-07-30 16:52:36 | DEBUG | 收到消息: {'MsgId': 2021596863, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n老板请吃老冰棍'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865569, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_BEW/7d0u|v1_sUyVzIad</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 老板请吃老冰棍', 'NewMsgId': 2120327889017014227, 'MsgSeq': 871412078}
2025-07-30 16:52:36 | INFO | 收到文本消息: 消息ID:2021596863 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:老板请吃老冰棍
2025-07-30 16:52:37 | DEBUG | 处理消息内容: '老板请吃老冰棍'
2025-07-30 16:52:37 | DEBUG | 消息内容 '老板请吃老冰棍' 不匹配任何命令，忽略
2025-07-30 16:52:39 | DEBUG | 收到消息: {'MsgId': 1646867303, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="533495ddb3c9379f9ec8b2807c5aa3bc" encryver="1" cdnthumbaeskey="533495ddb3c9379f9ec8b2807c5aa3bc" cdnthumburl="3057020100044b3049020100020468cde53a02032f55fa020455ff1c6502046889dd51042465653330356638342d633032312d343034642d393539392d663236636637366439353531020405250a020201000405004c550600" cdnthumblength="3468" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032f55fa020455ff1c6502046889dd51042465653330356638342d633032312d343034642d393539392d663236636637366439353531020405250a020201000405004c550600" length="350834" md5="7ab1bded1a6cb0afa5dcc3597ab6f141" hevc_mid_size="21812" originsourcemd5="5e3bda93f7172720a194f8a5d7daa500">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImI0MzBiMDIwMjAwMDAwMDAiLCJwZHFIYXNoIjoiNGQ4ZjExOWZmMTFkOWZkODQx\nMzkyNjFjNWE2NDI3YWM3MmU4ZWFhMTllYWQ5Yzg5YjExZDIzOTFhNjczMzQ3NyJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865569, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>107160f44eedcf7b506ebf10583195a6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PyEqkQab|v1_TT0PV7b8</signature>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 6929448914201379859, 'MsgSeq': 871412079}
2025-07-30 16:52:39 | INFO | 收到图片消息: 消息ID:1646867303 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="533495ddb3c9379f9ec8b2807c5aa3bc" encryver="1" cdnthumbaeskey="533495ddb3c9379f9ec8b2807c5aa3bc" cdnthumburl="3057020100044b3049020100020468cde53a02032f55fa020455ff1c6502046889dd51042465653330356638342d633032312d343034642d393539392d663236636637366439353531020405250a020201000405004c550600" cdnthumblength="3468" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032f55fa020455ff1c6502046889dd51042465653330356638342d633032312d343034642d393539392d663236636637366439353531020405250a020201000405004c550600" length="350834" md5="7ab1bded1a6cb0afa5dcc3597ab6f141" hevc_mid_size="21812" originsourcemd5="5e3bda93f7172720a194f8a5d7daa500"><secHashInfoBase64>eyJwaGFzaCI6ImI0MzBiMDIwMjAwMDAwMDAiLCJwZHFIYXNoIjoiNGQ4ZjExOWZmMTFkOWZkODQxMzkyNjFjNWE2NDI3YWM3MmU4ZWFhMTllYWQ5Yzg5YjExZDIzOTFhNjczMzQ3NyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 16:52:39 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-30 16:52:39 | INFO | [TimerTask] 缓存图片消息: 1646867303
2025-07-30 16:53:07 | DEBUG | 收到消息: {'MsgId': 1495441036, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_0oli6jrwn9ft22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>不是，但是我只做一次</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>6608080262355514526</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_xrb90gbzzu5q22</chatusr>\n\t\t\t<displayname>阿棉</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_ou2RV3CU|v1_t9y0iabf&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n队长吗你是我们这也是队长能领</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753857488</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_0oli6jrwn9ft22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865599, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>9f1e887075eaacfe896fd2514bd9fba2_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_vtN46kVx|v1_mV8fnVkH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8178425000910801613, 'MsgSeq': 871412080}
2025-07-30 16:53:07 | DEBUG | 从群聊消息中提取发送者: wxid_0oli6jrwn9ft22
2025-07-30 16:53:07 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 16:53:07 | INFO | 收到引用消息: 消息ID:1495441036 来自:27852221909@chatroom 发送人:wxid_0oli6jrwn9ft22 内容:不是，但是我只做一次 引用类型:1
2025-07-30 16:53:07 | INFO | [DouBaoImageToImage] 收到引用消息: 不是，但是我只做一次
2025-07-30 16:53:07 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 16:53:07 | INFO |   - 消息内容: 不是，但是我只做一次
2025-07-30 16:53:07 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-30 16:53:07 | INFO |   - 发送人: wxid_0oli6jrwn9ft22
2025-07-30 16:53:07 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n队长吗你是我们这也是队长能领', 'Msgid': '6608080262355514526', 'NewMsgId': '6608080262355514526', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '阿棉', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_ou2RV3CU|v1_t9y0iabf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753857488', 'SenderWxid': 'wxid_0oli6jrwn9ft22'}
2025-07-30 16:53:07 | INFO |   - 引用消息ID: 
2025-07-30 16:53:07 | INFO |   - 引用消息类型: 
2025-07-30 16:53:07 | INFO |   - 引用消息内容: 
队长吗你是我们这也是队长能领
2025-07-30 16:53:07 | INFO |   - 引用消息发送人: wxid_0oli6jrwn9ft22
2025-07-30 16:53:11 | DEBUG | 收到消息: {'MsgId': 1298894697, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_0oli6jrwn9ft22:\n<msg><emoji fromusername="wxid_0oli6jrwn9ft22" tousername="27852221909@chatroom" type="1" idbuffer="media:0_0" md5="9d16daaf0332b9521f339078f227a956" len="692345" productid="" androidmd5="9d16daaf0332b9521f339078f227a956" androidlen="692345" s60v3md5="9d16daaf0332b9521f339078f227a956" s60v3len="692345" s60v5md5="9d16daaf0332b9521f339078f227a956" s60v5len="692345" cdnurl="http://snsvideo.c2c.wechat.com/262/20304/stodownload?m=9d16daaf0332b9521f339078f227a956&amp;filekey=30350201010421301f020201060402484b04109d16daaf0332b9521f339078f227a95602030a9079040d00000004627466730000000131&amp;hy=HK&amp;storeid=32303231303632353133303630383030306533383239336338343730306363363961626130623030303030313036&amp;dotrans=0&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://snsvideo.c2c.wechat.com/262/20304/stodownload?m=e18e261e9715c0265a8ad159ac33e9e2&amp;filekey=30350201010421301f020201060402484b0410e18e261e9715c0265a8ad159ac33e9e202030a9080040d00000004627466730000000131&amp;hy=HK&amp;storeid=32303231303632353133303631303030303663336563336338343730306338323962626130623030303030313036&amp;dotrans=0&amp;bizid=1023" aeskey="08493f0a684f44b7af740948fa36a68a" externurl="http://snsvideo.c2c.wechat.com/262/20304/stodownload?m=42f72907ea028799b319ad1df0dd25a5&amp;filekey=30350201010421301f020201060402484b041042f72907ea028799b319ad1df0dd25a5020300d500040d00000004627466730000000131&amp;hy=HK&amp;storeid=32303231303632353133303631303030306563643430336338343730306363653965626130623030303030313036&amp;dotrans=0&amp;bizid=1023" externmd5="aa8965c793091ef8331eddc2db134af4" width="358" height="358" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="ChbpuYUg5ZOI5ZOI5ZOI5ZOI5ZOI5ZOI" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865603, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_7bBNi8cV|v1_95vuTKIr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2853875845751289775, 'MsgSeq': 871412081}
2025-07-30 16:53:11 | INFO | 收到表情消息: 消息ID:1298894697 来自:27852221909@chatroom 发送人:wxid_0oli6jrwn9ft22 MD5:9d16daaf0332b9521f339078f227a956 大小:692345
2025-07-30 16:53:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2853875845751289775
2025-07-30 16:54:32 | DEBUG | 收到消息: {'MsgId': 2118469389, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n好好好吃了一半了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865685, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_bZ30Qndj|v1_GmWU4PzC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 好好好吃了一半了', 'NewMsgId': 758837465680512808, 'MsgSeq': 871412082}
2025-07-30 16:54:32 | INFO | 收到文本消息: 消息ID:2118469389 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:好好好吃了一半了
2025-07-30 16:54:33 | DEBUG | 处理消息内容: '好好好吃了一半了'
2025-07-30 16:54:33 | DEBUG | 消息内容 '好好好吃了一半了' 不匹配任何命令，忽略
2025-07-30 16:55:37 | DEBUG | 收到消息: {'MsgId': 1894247396, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="2e44ebc3bb9b59725702c8e5160ba71a" encryver="1" cdnthumbaeskey="2e44ebc3bb9b59725702c8e5160ba71a" cdnthumburl="3057020100044b30490201000204d92c2cbd02032dd0e902046deae16a02046889da25042464303333316239302d613331342d346561632d383261362d3866303836303765366333310204052418020201000405004c53d900" cdnthumblength="3782" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d92c2cbd02032dd0e902046deae16a02046889da25042464303333316239302d613331342d346561632d383261362d3866303836303765366333310204052418020201000405004c53d900" length="90556" md5="1c7b37c8fcc0fae860e897ec63af5697" originsourcemd5="6cc6963e1aa88c3a5f69de31627b1482">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865749, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>70deaee9f4691f6fc4f5f0061b342aee_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_B/lFKlnR|v1_TfSkAB+m</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 1906674195703750998, 'MsgSeq': 871412083}
2025-07-30 16:55:37 | INFO | 收到图片消息: 消息ID:1894247396 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="2e44ebc3bb9b59725702c8e5160ba71a" encryver="1" cdnthumbaeskey="2e44ebc3bb9b59725702c8e5160ba71a" cdnthumburl="3057020100044b30490201000204d92c2cbd02032dd0e902046deae16a02046889da25042464303333316239302d613331342d346561632d383261362d3866303836303765366333310204052418020201000405004c53d900" cdnthumblength="3782" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d92c2cbd02032dd0e902046deae16a02046889da25042464303333316239302d613331342d346561632d383261362d3866303836303765366333310204052418020201000405004c53d900" length="90556" md5="1c7b37c8fcc0fae860e897ec63af5697" originsourcemd5="6cc6963e1aa88c3a5f69de31627b1482"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 16:55:37 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-30 16:55:37 | INFO | [TimerTask] 缓存图片消息: 1894247396
2025-07-30 16:56:14 | DEBUG | 收到消息: {'MsgId': 1940280280, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n这是要世界末日了吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865787, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_2nIv/3bC|v1_dyIiz28n</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 这是要世界末日了吗', 'NewMsgId': 855699349158963196, 'MsgSeq': 871412084}
2025-07-30 16:56:14 | INFO | 收到文本消息: 消息ID:1940280280 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:这是要世界末日了吗
2025-07-30 16:56:15 | DEBUG | 处理消息内容: '这是要世界末日了吗'
2025-07-30 16:56:15 | DEBUG | 消息内容 '这是要世界末日了吗' 不匹配任何命令，忽略
2025-07-30 16:56:21 | DEBUG | 收到消息: {'MsgId': 1746835436, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="0d00cb76e7bc7dedbb5ef679d75af13f" len="893627" productid="" androidmd5="0d00cb76e7bc7dedbb5ef679d75af13f" androidlen="893627" s60v3md5="0d00cb76e7bc7dedbb5ef679d75af13f" s60v3len="893627" s60v5md5="0d00cb76e7bc7dedbb5ef679d75af13f" s60v5len="893627" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=0d00cb76e7bc7dedbb5ef679d75af13f&amp;filekey=30440201010430302e02016e040253480420306430306362373665376263376465646262356566363739643735616631336602030da2bb040d00000004627466730000000132&amp;hy=SH&amp;storeid=26829cf0e00019ec580ff67240000006e01004fb2534829630031577b2d0dc&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=805b7f5eccef60be1f4bd4e3a64f89cb&amp;filekey=30440201010430302e02016e040253480420383035623766356563636566363062653166346264346533613634663839636202030da2c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26829cf0e00038bed80ff67240000006e02004fb2534829630031577b2d0eb&amp;ef=2&amp;bizid=1022" aeskey="913b75dc1887427fa78dd307cba36e50" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=931dc57d37c2b134e526ac07aa17f01a&amp;filekey=3043020101042f302d02016e040253480420393331646335376433376332623133346535323661633037616131376630316102025890040d00000004627466730000000132&amp;hy=SH&amp;storeid=26829cf0e0005986080ff67240000006e03004fb3534829630031577b2d0fa&amp;ef=3&amp;bizid=1022" externmd5="02ab3c41cfc7c82c34402e7e4ddf545a" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865794, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_WXRbSDSF|v1_nXGGmEcv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 3202119781643085514, 'MsgSeq': 871412085}
2025-07-30 16:56:21 | INFO | 收到表情消息: 消息ID:1746835436 来自:48097389945@chatroom 发送人:xiaomaochong MD5:0d00cb76e7bc7dedbb5ef679d75af13f 大小:893627
2025-07-30 16:56:22 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3202119781643085514
2025-07-30 16:56:39 | DEBUG | 收到消息: {'MsgId': 1171759197, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_nfjhvkja087s12:\n[呲牙]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865812, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_5YELJeO0|v1_ODkFutSe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7611467036368931306, 'MsgSeq': 871412086}
2025-07-30 16:56:39 | INFO | 收到表情消息: 消息ID:1171759197 来自:27852221909@chatroom 发送人:wxid_nfjhvkja087s12 @:[] 内容:[呲牙]
2025-07-30 16:56:39 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7611467036368931306
