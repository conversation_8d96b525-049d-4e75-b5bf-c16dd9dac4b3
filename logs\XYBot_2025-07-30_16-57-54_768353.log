2025-07-30 16:57:58 | SUCCESS | 读取主设置成功
2025-07-30 16:57:58 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-30 16:57:59 | INFO | 2025/07/30 16:57:59 GetRedisAddr: 127.0.0.1:6379
2025-07-30 16:57:59 | INFO | 2025/07/30 16:57:59 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-30 16:57:59 | INFO | 2025/07/30 16:57:59 Server start at :9000
2025-07-30 16:57:59 | INFO | 2025/07/30 16:57:59 Failed to start server: listen tcp :9000: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.
2025-07-30 16:57:59 | SUCCESS | WechatAPI服务已启动
2025-07-30 16:58:00 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-30 16:58:00 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-30 16:58:00 | SUCCESS | 登录成功
2025-07-30 16:58:00 | INFO | 自动心跳已在运行中
2025-07-30 16:58:00 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 16:58:00 | SUCCESS | 数据库初始化成功
2025-07-30 16:58:00 | SUCCESS | 定时任务已启动
2025-07-30 16:58:00 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-30 16:58:00 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 16:58:01 | INFO | 播客API初始化成功
2025-07-30 16:58:01 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 16:58:01 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 16:58:01 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-30 16:58:01 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-30 16:58:01 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-30 16:58:01 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-30 16:58:01 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-30 16:58:01 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-30 16:58:01 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-30 16:58:02 | INFO | [ChatSummary] 数据库初始化成功
2025-07-30 16:58:02 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-30 16:58:02 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-30 16:58:02 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-30 16:58:02 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-30 16:58:03 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-30 16:58:03 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-30 16:58:03 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-30 16:58:03 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 16:58:03 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-30 16:58:03 | INFO | [RenameReminder] 开始启用插件...
2025-07-30 16:58:03 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-30 16:58:03 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-30 16:58:03 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-30 16:58:03 | INFO | 已设置检查间隔为 3600 秒
2025-07-30 16:58:03 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-30 16:58:03 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-30 16:58:04 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-30 16:58:07 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-30 16:58:08 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-30 16:58:09 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-30 16:58:09 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 16:58:09 | INFO | [yuanbao] 插件初始化完成
2025-07-30 16:58:09 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-30 16:58:09 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-30 16:58:09 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-30 16:58:09 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-30 16:58:09 | INFO | 处理堆积消息中
2025-07-30 16:58:09 | DEBUG | 接受到 9 条消息
2025-07-30 16:58:11 | SUCCESS | 处理堆积消息完毕
2025-07-30 16:58:11 | SUCCESS | 开始处理消息
2025-07-30 16:58:20 | DEBUG | 收到消息: {'MsgId': 835156859, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1569585694414:\n@小爱\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865900, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[xiaomaochong]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_2Jd0V0F1|v1_/u0F07AL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '空白 : @小爱\u2005', 'NewMsgId': 6597782745460170137, 'MsgSeq': 871412087}
2025-07-30 16:58:20 | INFO | 收到文本消息: 消息ID:835156859 来自:48097389945@chatroom 发送人:wxid_1569585694414 @:['xiaomaochong'] 内容:@小爱 
2025-07-30 16:58:21 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 16:58:21 | DEBUG | 处理消息内容: '@小爱'
2025-07-30 16:58:21 | DEBUG | 消息内容 '@小爱' 不匹配任何命令，忽略
2025-07-30 16:58:23 | DEBUG | 收到消息: {'MsgId': 255695558, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n状态'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865909, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_yiKImaBr|v1_sPXjT3p9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 状态', 'NewMsgId': 3569711287586688274, 'MsgSeq': 871412088}
2025-07-30 16:58:23 | INFO | 收到文本消息: 消息ID:255695558 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:状态
2025-07-30 16:58:24 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:一切正常，已经43天20小时54分15秒
2025-07-30 16:58:24 | DEBUG | 处理消息内容: '状态'
2025-07-30 16:58:24 | DEBUG | 消息内容 '状态' 不匹配任何命令，忽略
2025-07-30 16:58:24 | DEBUG | 收到消息: {'MsgId': 893021365, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1569585694414:\ntg不来验证码怎么搞'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865909, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_KHe6ECf+|v1_fbiI8Scr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '空白 : tg不来验证码怎么搞', 'NewMsgId': 6273283412366420368, 'MsgSeq': 871412090}
2025-07-30 16:58:24 | INFO | 收到文本消息: 消息ID:893021365 来自:48097389945@chatroom 发送人:wxid_1569585694414 @:[] 内容:tg不来验证码怎么搞
2025-07-30 16:58:25 | DEBUG | 处理消息内容: 'tg不来验证码怎么搞'
2025-07-30 16:58:25 | DEBUG | 消息内容 'tg不来验证码怎么搞' 不匹配任何命令，忽略
2025-07-30 16:58:27 | DEBUG | 收到消息: {'MsgId': 280583795, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1569585694414:\n<msg><emoji fromusername = "wxid_1569585694414" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="0ffa65761215a23fa51d97beb1f59839" len = "915623" productid="" androidmd5="0ffa65761215a23fa51d97beb1f59839" androidlen="915623" s60v3md5 = "0ffa65761215a23fa51d97beb1f59839" s60v3len="915623" s60v5md5 = "0ffa65761215a23fa51d97beb1f59839" s60v5len="915623" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=0ffa65761215a23fa51d97beb1f59839&amp;filekey=30440201010430302e02016e0402535a0420306666613635373631323135613233666135316439376265623166353938333902030df8a7040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26727023800074ca1005743b20000006e01004fb1535a18866bc1e6c912244&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=d41ba8b8c73623cf7fd0746c5e4026e4&amp;filekey=30440201010430302e02016e0402535a0420643431626138623863373336323363663766643037343663356534303236653402030df8b0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267270238000916bc005743b20000006e02004fb2535a18866bc1e6c91226c&amp;ef=2&amp;bizid=1022" aeskey= "5b58a936c06e4d8aa757e491301bf9ff" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=23403701d536f539294606248e4c6e49&amp;filekey=30440201010430302e02016e0402535a042032333430333730316435333666353339323934363036323438653463366534390203011cc0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267270238000b365b005743b20000006e03004fb3535a18866bc1e6c912287&amp;ef=3&amp;bizid=1022" externmd5 = "e8c1b409314a8032c3470c66e76b38d2" width= "201" height= "201" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865910, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_WJNHRj7H|v1_5X9alQzv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '空白在群聊中发了一个表情', 'NewMsgId': 3134605578485641308, 'MsgSeq': 871412092}
2025-07-30 16:58:27 | INFO | 收到表情消息: 消息ID:280583795 来自:48097389945@chatroom 发送人:wxid_1569585694414 MD5:0ffa65761215a23fa51d97beb1f59839 大小:915623
2025-07-30 16:58:27 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3134605578485641308
2025-07-30 16:58:28 | DEBUG | 收到消息: {'MsgId': 929688384, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="4000" length="10543" bufid="0" aeskey="656563636c726a6575707a786d796c6b" voiceurl="3052020100044b304902010002049363814102033d14ba0204f939949d02046889deb7042432326330356661382d323730382d343732612d623338302d32343332353533333837313302040528000f02010004001dc74187" voicemd5="779b565dc5312eff739836e9c2fdd594" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_53438_1753865911" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865911, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_yWEFzt8e|v1_SBm394Z7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 1938958900771458538, 'MsgSeq': 871412093}
2025-07-30 16:58:28 | INFO | 收到语音消息: 消息ID:929688384 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="4000" length="10543" bufid="0" aeskey="656563636c726a6575707a786d796c6b" voiceurl="3052020100044b304902010002049363814102033d14ba0204f939949d02046889deb7042432326330356661382d323730382d343732612d623338302d32343332353533333837313302040528000f02010004001dc74187" voicemd5="779b565dc5312eff739836e9c2fdd594" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_53438_1753865911" fromusername="xiaomaochong" /></msg>
2025-07-30 16:58:35 | DEBUG | 收到消息: {'MsgId': 1592129245, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865921, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_H3blUp79|v1_ritMoNjy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女系列', 'NewMsgId': 4869188817528986008, 'MsgSeq': 871412094}
2025-07-30 16:58:35 | INFO | 收到文本消息: 消息ID:1592129245 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女系列
2025-07-30 16:58:36 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女系列 from wxid_ubbh6q832tcs21
2025-07-30 16:58:36 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女系列
2025-07-30 16:58:36 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 16:58:36 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: text
2025-07-30 16:58:36 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:搞不定了
2025-07-30 16:58:36 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 0.89秒
2025-07-30 16:58:36 | DEBUG | 处理消息内容: '找视频 美女系列'
2025-07-30 16:58:36 | DEBUG | 消息内容 '找视频 美女系列' 不匹配任何命令，忽略
2025-07-30 16:58:51 | DEBUG | 收到消息: {'MsgId': 1746586374, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n豆包 你是谁'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865937, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_x6MWNNSz|v1_d1XZ/Fgx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 豆包 你是谁', 'NewMsgId': 5613711178881661150, 'MsgSeq': 871412097}
2025-07-30 16:58:51 | INFO | 收到文本消息: 消息ID:1746586374 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:豆包 你是谁
2025-07-30 16:58:52 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:搞不出来
2025-07-30 16:58:52 | DEBUG | 处理消息内容: '豆包 你是谁'
2025-07-30 16:58:52 | DEBUG | 消息内容 '豆包 你是谁' 不匹配任何命令，忽略
2025-07-30 16:58:53 | DEBUG | 收到消息: {'MsgId': 1633530430, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>怕了吗</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>855699349158963196</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_PZdPn2KK|v1_a2tN+sfc&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n这是要世界末日了吗</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753865787</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865938, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>2eaf87eaabadebeb96f11c4494d894f4_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_tvSIRO5W|v1_T77rJmNr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 怕了吗', 'NewMsgId': 3208411114307188203, 'MsgSeq': 871412099}
2025-07-30 16:58:53 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-30 16:58:53 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 16:58:53 | INFO | 收到引用消息: 消息ID:1633530430 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:怕了吗 引用类型:1
2025-07-30 16:58:53 | INFO | [DouBaoImageToImage] 收到引用消息: 怕了吗
2025-07-30 16:58:53 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 16:58:53 | INFO |   - 消息内容: 怕了吗
2025-07-30 16:58:53 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-30 16:58:53 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-30 16:58:53 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n这是要世界末日了吗', 'Msgid': '855699349158963196', 'NewMsgId': '855699349158963196', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PZdPn2KK|v1_a2tN+sfc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753865787', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-30 16:58:53 | INFO |   - 引用消息ID: 
2025-07-30 16:58:53 | INFO |   - 引用消息类型: 
2025-07-30 16:58:53 | INFO |   - 引用消息内容: 
这是要世界末日了吗
2025-07-30 16:58:53 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-30 16:59:21 | DEBUG | 收到消息: {'MsgId': 783818985, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>西安啥事没得</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>3208411114307188203</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_wlnzvr8ivgd422</chatusr>\n\t\t\t<displayname>锦岚</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;4&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;2eaf87eaabadebeb96f11c4494d894f4_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_R3PoPspf|v1_JuFmumck&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;appmsg appid="" sdkver="0"&gt;\n\t\t&lt;title&gt;怕了吗&lt;/title&gt;\n\t\t&lt;des /&gt;\n\t\t&lt;username /&gt;\n\t\t&lt;action&gt;view&lt;/action&gt;\n\t\t&lt;type&gt;57&lt;/type&gt;\n\t\t&lt;showtype&gt;0&lt;/showtype&gt;\n\t\t&lt;content /&gt;\n\t\t&lt;url /&gt;\n\t\t&lt;lowurl /&gt;\n\t\t&lt;forwardflag&gt;0&lt;/forwardflag&gt;\n\t\t&lt;dataurl /&gt;\n\t\t&lt;lowdataurl /&gt;\n\t\t&lt;contentattr&gt;0&lt;/contentattr&gt;\n\t\t&lt;streamvideo&gt;\n\t\t\t&lt;streamvideourl /&gt;\n\t\t\t&lt;streamvideototaltime&gt;0&lt;/streamvideototaltime&gt;\n\t\t\t&lt;streamvideotitle /&gt;\n\t\t\t&lt;streamvideowording /&gt;\n\t\t\t&lt;streamvideoweburl /&gt;\n\t\t\t&lt;streamvideothumburl /&gt;\n\t\t\t&lt;streamvideoaduxinfo /&gt;\n\t\t\t&lt;streamvideopublishid /&gt;\n\t\t&lt;/streamvideo&gt;\n\t\t&lt;canvasPageItem&gt;\n\t\t\t&lt;canvasPageXml&gt;&lt;![CDATA[]]&gt;&lt;/canvasPageXml&gt;\n\t\t&lt;/canvasPageItem&gt;\n\t\t&lt;refermsg&gt;&lt;/refermsg&gt;\n\t\t&lt;appattach&gt;\n\t\t\t&lt;totallen&gt;0&lt;/totallen&gt;\n\t\t\t&lt;attachid /&gt;\n\t\t\t&lt;cdnattachurl /&gt;\n\t\t\t&lt;emoticonmd5&gt;&lt;/emoticonmd5&gt;\n\t\t\t&lt;aeskey&gt;&lt;/aeskey&gt;\n\t\t\t&lt;fileext /&gt;\n\t\t\t&lt;islargefilemsg&gt;0&lt;/islargefilemsg&gt;\n\t\t&lt;/appattach&gt;\n\t\t&lt;extinfo /&gt;\n\t\t&lt;androidsource&gt;0&lt;/androidsource&gt;\n\t\t&lt;thumburl /&gt;\n\t\t&lt;mediatagname /&gt;\n\t\t&lt;messageaction&gt;&lt;![CDATA[]]&gt;&lt;/messageaction&gt;\n\t\t&lt;messageext&gt;&lt;![CDATA[]]&gt;&lt;/messageext&gt;\n\t\t&lt;emoticongift&gt;\n\t\t\t&lt;packageflag&gt;0&lt;/packageflag&gt;\n\t\t\t&lt;packageid /&gt;\n\t\t&lt;/emoticongift&gt;\n\t\t&lt;emoticonshared&gt;\n\t\t\t&lt;packageflag&gt;0&lt;/packageflag&gt;\n\t\t\t&lt;packageid /&gt;\n\t\t&lt;/emoticonshared&gt;\n\t\t&lt;designershared&gt;\n\t\t\t&lt;designeruin&gt;0&lt;/designeruin&gt;\n\t\t\t&lt;designername&gt;null&lt;/designername&gt;\n\t\t\t&lt;designerrediretcturl&gt;&lt;![CDATA[null]]&gt;&lt;/designerrediretcturl&gt;\n\t\t&lt;/designershared&gt;\n\t\t&lt;emotionpageshared&gt;\n\t\t\t&lt;tid&gt;0&lt;/tid&gt;\n\t\t\t&lt;title&gt;null&lt;/title&gt;\n\t\t\t&lt;desc&gt;null&lt;/desc&gt;\n\t\t\t&lt;iconUrl&gt;&lt;![CDATA[null]]&gt;&lt;/iconUrl&gt;\n\t\t\t&lt;secondUrl&gt;null&lt;/secondUrl&gt;\n\t\t\t&lt;pageType&gt;0&lt;/pageType&gt;\n\t\t\t&lt;setKey&gt;null&lt;/setKey&gt;\n\t\t&lt;/emotionpageshared&gt;\n\t\t&lt;webviewshared&gt;\n\t\t\t&lt;shareUrlOriginal /&gt;\n\t\t\t&lt;shareUrlOpen /&gt;\n\t\t\t&lt;jsAppId /&gt;\n\t\t\t&lt;publisherId /&gt;\n\t\t\t&lt;publisherReqId /&gt;\n\t\t&lt;/webviewshared&gt;\n\t\t&lt;template_id /&gt;\n\t\t&lt;md5 /&gt;\n\t\t&lt;websearch&gt;\n\t\t\t&lt;rec_category&gt;0&lt;/rec_category&gt;\n\t\t\t&lt;channelId&gt;0&lt;/channelId&gt;\n\t\t&lt;/websearch&gt;\n\t\t&lt;weappinfo&gt;\n\t\t\t&lt;username /&gt;\n\t\t\t&lt;appid /&gt;\n\t\t\t&lt;appservicetype&gt;0&lt;/appservicetype&gt;\n\t\t\t&lt;secflagforsinglepagemode&gt;0&lt;/secflagforsinglepagemode&gt;\n\t\t\t&lt;videopageinfo&gt;\n\t\t\t\t&lt;thumbwidth&gt;0&lt;/thumbwidth&gt;\n\t\t\t\t&lt;thumbheight&gt;0&lt;/thumbheight&gt;\n\t\t\t\t&lt;fromopensdk&gt;0&lt;/fromopensdk&gt;\n\t\t\t&lt;/videopageinfo&gt;\n\t\t&lt;/weappinfo&gt;\n\t\t&lt;statextstr /&gt;\n\t\t&lt;musicShareItem&gt;\n\t\t\t&lt;musicDuration&gt;0&lt;/musicDuration&gt;\n\t\t&lt;/musicShareItem&gt;\n\t\t&lt;finderLiveProductShare&gt;\n\t\t\t&lt;finderLiveID&gt;&lt;![CDATA[]]&gt;&lt;/finderLiveID&gt;\n\t\t\t&lt;finderUsername&gt;&lt;![CDATA[]]&gt;&lt;/finderUsername&gt;\n\t\t\t&lt;finderObjectID&gt;&lt;![CDATA[]]&gt;&lt;/finderObjectID&gt;\n\t\t\t&lt;finderNonceID&gt;&lt;![CDATA[]]&gt;&lt;/finderNonceID&gt;\n\t\t\t&lt;liveStatus&gt;&lt;![CDATA[]]&gt;&lt;/liveStatus&gt;\n\t\t\t&lt;appId&gt;&lt;![CDATA[]]&gt;&lt;/appId&gt;\n\t\t\t&lt;pagePath&gt;&lt;![CDATA[]]&gt;&lt;/pagePath&gt;\n\t\t\t&lt;productId&gt;&lt;![CDATA[]]&gt;&lt;/productId&gt;\n\t\t\t&lt;coverUrl&gt;&lt;![CDATA[]]&gt;&lt;/coverUrl&gt;\n\t\t\t&lt;productTitle&gt;&lt;![CDATA[]]&gt;&lt;/productTitle&gt;\n\t\t\t&lt;marketPrice&gt;&lt;![CDATA[0]]&gt;&lt;/marketPrice&gt;\n\t\t\t&lt;sellingPrice&gt;&lt;![CDATA[0]]&gt;&lt;/sellingPrice&gt;\n\t\t\t&lt;platformHeadImg&gt;&lt;![CDATA[]]&gt;&lt;/platformHeadImg&gt;\n\t\t\t&lt;platformName&gt;&lt;![CDATA[]]&gt;&lt;/platformName&gt;\n\t\t\t&lt;shopWindowId&gt;&lt;![CDATA[]]&gt;&lt;/shopWindowId&gt;\n\t\t\t&lt;flashSalePrice&gt;&lt;![CDATA[0]]&gt;&lt;/flashSalePrice&gt;\n\t\t\t&lt;flashSaleEndTime&gt;&lt;![CDATA[0]]&gt;&lt;/flashSaleEndTime&gt;\n\t\t\t&lt;ecSource&gt;&lt;![CDATA[]]&gt;&lt;/ecSource&gt;\n\t\t\t&lt;sellingPriceWording&gt;&lt;![CDATA[]]&gt;&lt;/sellingPriceWording&gt;\n\t\t\t&lt;platformIconURL&gt;&lt;![CDATA[]]&gt;&lt;/platformIconURL&gt;\n\t\t\t&lt;firstProductTagURL&gt;&lt;![CDATA[]]&gt;&lt;/firstProductTagURL&gt;\n\t\t\t&lt;firstProductTagAspectRatioString&gt;&lt;![CDATA[0.0]]&gt;&lt;/firstProductTagAspectRatioString&gt;\n\t\t\t&lt;secondProductTagURL&gt;&lt;![CDATA[]]&gt;&lt;/secondProductTagURL&gt;\n\t\t\t&lt;secondProductTagAspectRatioString&gt;&lt;![CDATA[0.0]]&gt;&lt;/secondProductTagAspectRatioString&gt;\n\t\t\t&lt;firstGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/firstGuaranteeWording&gt;\n\t\t\t&lt;secondGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/secondGuaranteeWording&gt;\n\t\t\t&lt;thirdGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/thirdGuaranteeWording&gt;\n\t\t\t&lt;isPriceBeginShow&gt;false&lt;/isPriceBeginShow&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t\t&lt;promoterKey&gt;&lt;![CDATA[]]&gt;&lt;/promoterKey&gt;\n\t\t\t&lt;discountWording&gt;&lt;![CDATA[]]&gt;&lt;/discountWording&gt;\n\t\t\t&lt;priceSuffixDescription&gt;&lt;![CDATA[]]&gt;&lt;/priceSuffixDescription&gt;\n\t\t\t&lt;productCardKey&gt;&lt;![CDATA[]]&gt;&lt;/productCardKey&gt;\n\t\t\t&lt;isWxShop&gt;&lt;![CDATA[]]&gt;&lt;/isWxShop&gt;\n\t\t\t&lt;brandIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/brandIconUrl&gt;\n\t\t\t&lt;rIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrl&gt;\n\t\t\t&lt;rIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrlDarkMode&gt;\n\t\t\t&lt;topShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrl&gt;\n\t\t\t&lt;topShopIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrlDarkMode&gt;\n\t\t\t&lt;simplifyTopShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrl&gt;\n\t\t\t&lt;simplifyTopShopIconUrlDarkmode&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrlDarkmode&gt;\n\t\t\t&lt;topShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconWidth&gt;\n\t\t\t&lt;topShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconHeight&gt;\n\t\t\t&lt;simplifyTopShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconWidth&gt;\n\t\t\t&lt;simplifyTopShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconHeight&gt;\n\t\t\t&lt;showBoxItemStringList /&gt;\n\t\t&lt;/finderLiveProductShare&gt;\n\t\t&lt;finderOrder&gt;\n\t\t\t&lt;appID&gt;&lt;![CDATA[]]&gt;&lt;/appID&gt;\n\t\t\t&lt;orderID&gt;&lt;![CDATA[]]&gt;&lt;/orderID&gt;\n\t\t\t&lt;path&gt;&lt;![CDATA[]]&gt;&lt;/path&gt;\n\t\t\t&lt;priceWording&gt;&lt;![CDATA[]]&gt;&lt;/priceWording&gt;\n\t\t\t&lt;stateWording&gt;&lt;![CDATA[]]&gt;&lt;/stateWording&gt;\n\t\t\t&lt;productImageURL&gt;&lt;![CDATA[]]&gt;&lt;/productImageURL&gt;\n\t\t\t&lt;products&gt;&lt;![CDATA[]]&gt;&lt;/products&gt;\n\t\t\t&lt;productsCount&gt;&lt;![CDATA[0]]&gt;&lt;/productsCount&gt;\n\t\t\t&lt;orderType&gt;&lt;![CDATA[0]]&gt;&lt;/orderType&gt;\n\t\t\t&lt;newPriceWording&gt;&lt;![CDATA[]]&gt;&lt;/newPriceWording&gt;\n\t\t\t&lt;newStateWording&gt;&lt;![CDATA[]]&gt;&lt;/newStateWording&gt;\n\t\t\t&lt;useNewWording&gt;&lt;![CDATA[0]]&gt;&lt;/useNewWording&gt;\n\t\t&lt;/finderOrder&gt;\n\t\t&lt;finderShopWindowShare&gt;\n\t\t\t&lt;finderUsername&gt;&lt;![CDATA[]]&gt;&lt;/finderUsername&gt;\n\t\t\t&lt;avatar&gt;&lt;![CDATA[]]&gt;&lt;/avatar&gt;\n\t\t\t&lt;nickname&gt;&lt;![CDATA[]]&gt;&lt;/nickname&gt;\n\t\t\t&lt;commodityInStockCount&gt;&lt;![CDATA[]]&gt;&lt;/commodityInStockCount&gt;\n\t\t\t&lt;appId&gt;&lt;![CDATA[]]&gt;&lt;/appId&gt;\n\t\t\t&lt;path&gt;&lt;![CDATA[]]&gt;&lt;/path&gt;\n\t\t\t&lt;appUsername&gt;&lt;![CDATA[]]&gt;&lt;/appUsername&gt;\n\t\t\t&lt;query&gt;&lt;![CDATA[]]&gt;&lt;/query&gt;\n\t\t\t&lt;liteAppId&gt;&lt;![CDATA[]]&gt;&lt;/liteAppId&gt;\n\t\t\t&lt;liteAppPath&gt;&lt;![CDATA[]]&gt;&lt;/liteAppPath&gt;\n\t\t\t&lt;liteAppQuery&gt;&lt;![CDATA[]]&gt;&lt;/liteAppQuery&gt;\n\t\t\t&lt;platformTagURL&gt;&lt;![CDATA[]]&gt;&lt;/platformTagURL&gt;\n\t\t\t&lt;saleWording&gt;&lt;![CDATA[]]&gt;&lt;/saleWording&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t\t&lt;profileTypeWording&gt;&lt;![CDATA[]]&gt;&lt;/profileTypeWording&gt;\n\t\t\t&lt;saleWordingExtra&gt;&lt;![CDATA[]]&gt;&lt;/saleWordingExtra&gt;\n\t\t\t&lt;isWxShop&gt;&lt;![CDATA[]]&gt;&lt;/isWxShop&gt;\n\t\t\t&lt;platformIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/platformIconUrl&gt;\n\t\t\t&lt;brandIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/brandIconUrl&gt;\n\t\t\t&lt;description&gt;&lt;![CDATA[]]&gt;&lt;/description&gt;\n\t\t\t&lt;backgroundUrl&gt;&lt;![CDATA[]]&gt;&lt;/backgroundUrl&gt;\n\t\t\t&lt;darkModePlatformIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/darkModePlatformIconUrl&gt;\n\t\t\t&lt;rIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrl&gt;\n\t\t\t&lt;rIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrlDarkMode&gt;\n\t\t\t&lt;rWords&gt;&lt;![CDATA[]]&gt;&lt;/rWords&gt;\n\t\t\t&lt;topShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrl&gt;\n\t\t\t&lt;topShopIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrlDarkMode&gt;\n\t\t\t&lt;simplifyTopShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrl&gt;\n\t\t\t&lt;simplifyTopShopIconUrlDarkmode&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrlDarkmode&gt;\n\t\t\t&lt;topShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconWidth&gt;\n\t\t\t&lt;topShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconHeight&gt;\n\t\t\t&lt;simplifyTopShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconWidth&gt;\n\t\t\t&lt;simplifyTopShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconHeight&gt;\n\t\t\t&lt;reputationInfo&gt;\n\t\t\t\t&lt;hasReputationInfo&gt;0&lt;/hasReputationInfo&gt;\n\t\t\t\t&lt;reputationScore&gt;0&lt;/reputationScore&gt;\n\t\t\t\t&lt;reputationWording /&gt;\n\t\t\t\t&lt;reputationTextColor /&gt;\n\t\t\t\t&lt;reputationLevelWording /&gt;\n\t\t\t\t&lt;reputationBackgroundColor /&gt;\n\t\t\t&lt;/reputationInfo&gt;\n\t\t\t&lt;productImageURLList /&gt;\n\t\t&lt;/finderShopWindowShare&gt;\n\t\t&lt;findernamecard&gt;\n\t\t\t&lt;username /&gt;\n\t\t\t&lt;avatar&gt;&lt;![CDATA[]]&gt;&lt;/avatar&gt;\n\t\t\t&lt;nickname /&gt;\n\t\t\t&lt;auth_job /&gt;\n\t\t\t&lt;auth_icon&gt;0&lt;/auth_icon&gt;\n\t\t\t&lt;auth_icon_url /&gt;\n\t\t\t&lt;ecSource&gt;&lt;![CDATA[]]&gt;&lt;/ecSource&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t&lt;/findernamecard&gt;\n\t\t&lt;finderGuarantee&gt;\n\t\t\t&lt;scene&gt;&lt;![CDATA[0]]&gt;&lt;/scene&gt;\n\t\t&lt;/finderGuarantee&gt;\n\t\t&lt;directshare&gt;0&lt;/directshare&gt;\n\t\t&lt;gamecenter&gt;\n\t\t\t&lt;namecard&gt;\n\t\t\t\t&lt;iconUrl /&gt;\n\t\t\t\t&lt;name /&gt;\n\t\t\t\t&lt;desc /&gt;\n\t\t\t\t&lt;tail /&gt;\n\t\t\t\t&lt;jumpUrl /&gt;\n\t\t\t\t&lt;liteappId /&gt;\n\t\t\t\t&lt;liteappPath /&gt;\n\t\t\t\t&lt;liteappQuery /&gt;\n\t\t\t\t&lt;liteappMinVersion /&gt;\n\t\t\t&lt;/namecard&gt;\n\t\t&lt;/gamecenter&gt;\n\t\t&lt;patMsg&gt;\n\t\t\t&lt;chatUser /&gt;\n\t\t\t&lt;records&gt;\n\t\t\t\t&lt;recordNum&gt;0&lt;/recordNum&gt;\n\t\t\t&lt;/records&gt;\n\t\t&lt;/patMsg&gt;\n\t\t&lt;secretmsg&gt;\n\t\t\t&lt;issecretmsg&gt;0&lt;/issecretmsg&gt;\n\t\t&lt;/secretmsg&gt;\n\t\t&lt;referfromscene&gt;0&lt;/referfromscene&gt;\n\t\t&lt;gameshare&gt;\n\t\t\t&lt;liteappext&gt;\n\t\t\t\t&lt;liteappbizdata /&gt;\n\t\t\t\t&lt;priority&gt;0&lt;/priority&gt;\n\t\t\t&lt;/liteappext&gt;\n\t\t\t&lt;appbrandext&gt;\n\t\t\t\t&lt;litegameinfo /&gt;\n\t\t\t\t&lt;priority&gt;-1&lt;/priority&gt;\n\t\t\t&lt;/appbrandext&gt;\n\t\t\t&lt;gameshareid /&gt;\n\t\t\t&lt;sharedata /&gt;\n\t\t\t&lt;isvideo&gt;0&lt;/isvideo&gt;\n\t\t\t&lt;duration&gt;-1&lt;/duration&gt;\n\t\t\t&lt;isexposed&gt;0&lt;/isexposed&gt;\n\t\t\t&lt;readtext /&gt;\n\t\t&lt;/gameshare&gt;\n\t\t&lt;tingChatRoomItem&gt;\n\t\t\t&lt;type&gt;0&lt;/type&gt;\n\t\t\t&lt;categoryItem&gt;null&lt;/categoryItem&gt;\n\t\t\t&lt;categoryId /&gt;\n\t\t&lt;/tingChatRoomItem&gt;\n\t\t&lt;mpsharetrace&gt;\n\t\t\t&lt;hasfinderelement&gt;0&lt;/hasfinderelement&gt;\n\t\t\t&lt;lastgmsgid /&gt;\n\t\t&lt;/mpsharetrace&gt;\n\t\t&lt;wxgamecard&gt;\n\t\t\t&lt;framesetname /&gt;\n\t\t\t&lt;mbcarddata /&gt;\n\t\t\t&lt;minpkgversion /&gt;\n\t\t\t&lt;clientextinfo /&gt;\n\t\t\t&lt;mbcardheight&gt;0&lt;/mbcardheight&gt;\n\t\t\t&lt;isoldversion&gt;0&lt;/isoldversion&gt;\n\t\t&lt;/wxgamecard&gt;\n\t\t&lt;ecskfcard&gt;\n\t\t\t&lt;framesetname /&gt;\n\t\t\t&lt;mbcarddata /&gt;\n\t\t\t&lt;minupdateunixtimestamp&gt;0&lt;/minupdateunixtimestamp&gt;\n\t\t\t&lt;needheader&gt;false&lt;/needheader&gt;\n\t\t\t&lt;summary /&gt;\n\t\t&lt;/ecskfcard&gt;\n\t\t&lt;liteapp&gt;\n\t\t\t&lt;id&gt;null&lt;/id&gt;\n\t\t\t&lt;path /&gt;\n\t\t\t&lt;query /&gt;\n\t\t\t&lt;istransparent&gt;0&lt;/istransparent&gt;\n\t\t\t&lt;hideicon&gt;0&lt;/hideicon&gt;\n\t\t\t&lt;forbidforward&gt;0&lt;/forbidforward&gt;\n\t\t&lt;/liteapp&gt;\n\t\t&lt;opensdk_share_is_modified&gt;0&lt;/opensdk_share_is_modified&gt;\n\t&lt;/appmsg&gt;\n\t&lt;fromusername&gt;wxid_wlnzvr8ivgd422&lt;/fromusername&gt;\n\t&lt;scene&gt;0&lt;/scene&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;version&gt;1&lt;/version&gt;\n\t\t&lt;appname /&gt;\n\t&lt;/appinfo&gt;\n\t&lt;commenturl /&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753865938</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865966, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>2eaf87eaabadebeb96f11c4494d894f4_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_QLNPshHF|v1_kjSXiouP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 西安啥事没得', 'NewMsgId': 6496657717432308214, 'MsgSeq': 871412103}
2025-07-30 16:59:21 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-30 16:59:21 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 16:59:21 | INFO | 收到引用消息: 消息ID:783818985 来自:48097389945@chatroom 发送人:xiaomaochong 内容:西安啥事没得 引用类型:49
2025-07-30 16:59:22 | INFO | [DouBaoImageToImage] 收到引用消息: 西安啥事没得
2025-07-30 16:59:22 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 16:59:22 | INFO |   - 消息内容: 西安啥事没得
2025-07-30 16:59:22 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-30 16:59:22 | INFO |   - 发送人: xiaomaochong
2025-07-30 16:59:22 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>怕了吗</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg></refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n', 'Msgid': '3208411114307188203', 'NewMsgId': '3208411114307188203', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '锦岚', 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>2eaf87eaabadebeb96f11c4494d894f4_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_R3PoPspf|v1_JuFmumck</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753865938', 'SenderWxid': 'xiaomaochong'}
2025-07-30 16:59:22 | INFO |   - 引用消息ID: 
2025-07-30 16:59:22 | INFO |   - 引用消息类型: 
2025-07-30 16:59:22 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>怕了吗</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg></refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 16:59:22 | INFO |   - 引用消息发送人: xiaomaochong
2025-07-30 16:59:29 | DEBUG | 收到消息: {'MsgId': 1039267374, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865975, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_LA769wGm|v1_5BH7qVEU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女系列', 'NewMsgId': 3577918648427375753, 'MsgSeq': 871412104}
2025-07-30 16:59:29 | INFO | 收到文本消息: 消息ID:1039267374 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女系列
2025-07-30 16:59:29 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女系列 from wxid_ubbh6q832tcs21
2025-07-30 16:59:29 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女系列
2025-07-30 16:59:30 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 16:59:30 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: text
2025-07-30 16:59:30 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:弄不了
2025-07-30 16:59:30 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 0.70秒
2025-07-30 16:59:30 | DEBUG | 处理消息内容: '找视频 美女系列'
2025-07-30 16:59:30 | DEBUG | 消息内容 '找视频 美女系列' 不匹配任何命令，忽略
2025-07-30 16:59:45 | DEBUG | 收到消息: {'MsgId': 521065240, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n沿海地区要注意安全'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753865989, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_wAQTOylu|v1_z1SQ8+3E</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 沿海地区要注意安全', 'NewMsgId': 6542757025113916466, 'MsgSeq': 871412107}
2025-07-30 16:59:45 | INFO | 收到文本消息: 消息ID:521065240 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:沿海地区要注意安全
2025-07-30 16:59:46 | DEBUG | 处理消息内容: '沿海地区要注意安全'
2025-07-30 16:59:46 | DEBUG | 消息内容 '沿海地区要注意安全' 不匹配任何命令，忽略
2025-07-30 17:00:15 | DEBUG | 收到消息: {'MsgId': 1322020579, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>谷歌全套装上</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>6273283412366420368</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_1569585694414</chatusr>\n\t\t\t<displayname>空白</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_IjEVOLE/|v1_Aj9GUfTh&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\ntg不来验证码怎么搞</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753865909</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753866020, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>de9bc9e089c1c0ec003567004ca0db47_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Wt2ddI7X|v1_2Q03ZoaX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 谷歌全套装上', 'NewMsgId': 5411712943575453874, 'MsgSeq': 871412108}
2025-07-30 17:00:15 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-30 17:00:15 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 17:00:15 | INFO | 收到引用消息: 消息ID:1322020579 来自:48097389945@chatroom 发送人:xiaomaochong 内容:谷歌全套装上 引用类型:1
2025-07-30 17:00:15 | INFO | [DouBaoImageToImage] 收到引用消息: 谷歌全套装上
2025-07-30 17:00:15 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 17:00:15 | INFO |   - 消息内容: 谷歌全套装上
2025-07-30 17:00:15 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-30 17:00:15 | INFO |   - 发送人: xiaomaochong
2025-07-30 17:00:15 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\ntg不来验证码怎么搞', 'Msgid': '6273283412366420368', 'NewMsgId': '6273283412366420368', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '空白', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_IjEVOLE/|v1_Aj9GUfTh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753865909', 'SenderWxid': 'xiaomaochong'}
2025-07-30 17:00:15 | INFO |   - 引用消息ID: 
2025-07-30 17:00:15 | INFO |   - 引用消息类型: 
2025-07-30 17:00:15 | INFO |   - 引用消息内容: 
tg不来验证码怎么搞
2025-07-30 17:00:15 | INFO |   - 引用消息发送人: xiaomaochong
2025-07-30 17:00:32 | DEBUG | 收到消息: {'MsgId': 344480977, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n开启美图模式'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753866038, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_dkZfoQS8|v1_yrbPqIu2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 开启美图模式', 'NewMsgId': 1075412100588006139, 'MsgSeq': 871412109}
2025-07-30 17:00:32 | INFO | 收到文本消息: 消息ID:344480977 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:开启美图模式
2025-07-30 17:00:33 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅ 已进入美图AI模式，正在初始化...
2025-07-30 17:00:42 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:你好呀！我是你的AI影像创作助手RoboNeo~ 无论是修图、做设计还是视频剪辑，只要告诉我你的想法，我就能帮你轻松实现！今天想创作点什么呢？✨
2025-07-30 17:00:43 | DEBUG | 收到消息: {'MsgId': 1408379756, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n用邮件收[抠鼻]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753866040, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_1u7OSuxR|v1_cyVNOGE0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 用邮件收[抠鼻]', 'NewMsgId': 8871998094665301144, 'MsgSeq': 871412112}
2025-07-30 17:00:43 | INFO | 收到文本消息: 消息ID:1408379756 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:用邮件收[抠鼻]
2025-07-30 17:00:43 | DEBUG | 处理消息内容: '用邮件收[抠鼻]'
2025-07-30 17:00:43 | DEBUG | 消息内容 '用邮件收[抠鼻]' 不匹配任何命令，忽略
2025-07-30 17:00:59 | DEBUG | 收到消息: {'MsgId': 1427224950, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="599b9a0e7afb4d29b2911e62ece875b1" cdnvideourl="3057020100044b304902010002049363814102032f51490204df31227502046889df50042435663737376564372d336239652d346135312d386637312d6133646335353839386538630204052408040201000405004c550500" cdnthumbaeskey="599b9a0e7afb4d29b2911e62ece875b1" cdnthumburl="3057020100044b304902010002049363814102032f51490204df31227502046889df50042435663737376564372d336239652d346135312d386637312d6133646335353839386538630204052408040201000405004c550500" length="1441066" playlength="16" cdnthumblength="5882" cdnthumbwidth="442" cdnthumbheight="786" fromusername="xiaomaochong" md5="3202a42aa92118d031bc011917bb8e59" newmd5="53fd2491578a4c9f0d69f947132810db" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753866065, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<videopreloadlen>469306</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>5e125602e6101c9a757b023e595f2b2e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_bLtepM4a|v1_tlD2DSBL</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段视频', 'NewMsgId': 413226755151347790, 'MsgSeq': 871412117}
2025-07-30 17:00:59 | INFO | 收到视频消息: 消息ID:1427224950 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="599b9a0e7afb4d29b2911e62ece875b1" cdnvideourl="3057020100044b304902010002049363814102032f51490204df31227502046889df50042435663737376564372d336239652d346135312d386637312d6133646335353839386538630204052408040201000405004c550500" cdnthumbaeskey="599b9a0e7afb4d29b2911e62ece875b1" cdnthumburl="3057020100044b304902010002049363814102032f51490204df31227502046889df50042435663737376564372d336239652d346135312d386637312d6133646335353839386538630204052408040201000405004c550500" length="1441066" playlength="16" cdnthumblength="5882" cdnthumbwidth="442" cdnthumbheight="786" fromusername="xiaomaochong" md5="3202a42aa92118d031bc011917bb8e59" newmd5="53fd2491578a4c9f0d69f947132810db" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

