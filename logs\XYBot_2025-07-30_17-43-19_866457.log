2025-07-30 17:43:21 | SUCCESS | 读取主设置成功
2025-07-30 17:43:21 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-30 17:43:21 | INFO | 2025/07/30 17:43:21 GetRedisAddr: 127.0.0.1:6379
2025-07-30 17:43:21 | INFO | 2025/07/30 17:43:21 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-30 17:43:21 | INFO | 2025/07/30 17:43:21 Server start at :9000
2025-07-30 17:43:21 | SUCCESS | WechatAPI服务已启动
2025-07-30 17:43:22 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-30 17:43:22 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-30 17:43:22 | SUCCESS | 登录成功
2025-07-30 17:43:22 | SUCCESS | 已开启自动心跳
2025-07-30 17:43:22 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 17:43:22 | SUCCESS | 数据库初始化成功
2025-07-30 17:43:22 | SUCCESS | 定时任务已启动
2025-07-30 17:43:22 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-30 17:43:22 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:43:23 | INFO | 播客API初始化成功
2025-07-30 17:43:23 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 17:43:23 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 17:43:23 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-30 17:43:23 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-30 17:43:23 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-30 17:43:23 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-30 17:43:23 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-30 17:43:23 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-30 17:43:23 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-30 17:43:23 | INFO | [ChatSummary] 数据库初始化成功
2025-07-30 17:43:24 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-07-30 17:43:24 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-07-30 17:43:24 | DEBUG |   - 启用状态: True
2025-07-30 17:43:24 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-07-30 17:43:24 | DEBUG |   - 设备ID: 7468716989062841895
2025-07-30 17:43:24 | DEBUG |   - Web ID: 7468716986638386703
2025-07-30 17:43:24 | DEBUG |   - Cookies配置: 已配置
2025-07-30 17:43:24 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-07-30 17:43:24 | DEBUG |   - 自然化响应: True
2025-07-30 17:43:24 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-30 17:43:24 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-30 17:43:24 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-30 17:43:24 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-30 17:43:24 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-30 17:43:24 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-30 17:43:24 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-30 17:43:24 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:43:24 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-30 17:43:24 | INFO | [RenameReminder] 开始启用插件...
2025-07-30 17:43:24 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-30 17:43:24 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-30 17:43:24 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-30 17:43:24 | INFO | 已设置检查间隔为 3600 秒
2025-07-30 17:43:24 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-30 17:43:24 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-30 17:43:24 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-30 17:43:25 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-30 17:43:25 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-30 17:43:25 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-30 17:43:25 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 17:43:25 | INFO | [yuanbao] 插件初始化完成
2025-07-30 17:43:25 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-30 17:43:25 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-30 17:43:25 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-30 17:43:25 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-30 17:43:25 | INFO | 处理堆积消息中
2025-07-30 17:43:26 | DEBUG | 接受到 1 条消息
2025-07-30 17:43:27 | DEBUG | 接受到 1 条消息
2025-07-30 17:43:28 | SUCCESS | 处理堆积消息完毕
2025-07-30 17:43:28 | SUCCESS | 开始处理消息
2025-07-30 17:44:07 | DEBUG | 收到消息: {'MsgId': 1884888632, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_002lrj9uidgz22:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="acc6980c22085af577752413379bbb48" cdnvideourl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e961042434333238643937322d366662352d343165342d383938642d35313834333962326232343702040d2408040201000405004c51e500" cdnthumbaeskey="acc6980c22085af577752413379bbb48" cdnthumburl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e961042434333238643937322d366662352d343165342d383938642d35313834333962326232343702040d2408040201000405004c51e500" length="1092805" playlength="11" cdnthumblength="13651" cdnthumbwidth="288" cdnthumbheight="512" fromusername="wxid_002lrj9uidgz22" md5="775039ba0a973b5eb0da6a51b3f94237" newmd5="d875b7c01eaad81f735105a1aee0a648" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868647, 'MsgSource': '<msgsource>\n\t<videopreloadlen>510048</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>324c61c692e3c0472a4f6a4c1d15bf44_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_djtiZ/3j|v1_arabEqNF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '7在群聊中发了一段视频', 'NewMsgId': 3113782222970761584, 'MsgSeq': 871412214}
2025-07-30 17:44:07 | INFO | 收到视频消息: 消息ID:1884888632 来自:48097389945@chatroom 发送人:wxid_002lrj9uidgz22 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="acc6980c22085af577752413379bbb48" cdnvideourl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e961042434333238643937322d366662352d343165342d383938642d35313834333962326232343702040d2408040201000405004c51e500" cdnthumbaeskey="acc6980c22085af577752413379bbb48" cdnthumburl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e961042434333238643937322d366662352d343165342d383938642d35313834333962326232343702040d2408040201000405004c51e500" length="1092805" playlength="11" cdnthumblength="13651" cdnthumbwidth="288" cdnthumbheight="512" fromusername="wxid_002lrj9uidgz22" md5="775039ba0a973b5eb0da6a51b3f94237" newmd5="d875b7c01eaad81f735105a1aee0a648" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-07-30 17:44:31 | DEBUG | 收到消息: {'MsgId': 1072269021, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_rwfb9vuy93jn22:\n搞不出来'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868670, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_CRP5w1km|v1_S6VXXvqL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '黹忙 : 搞不出来', 'NewMsgId': 3678252437151174859, 'MsgSeq': 871412215}
2025-07-30 17:44:31 | INFO | 收到文本消息: 消息ID:1072269021 来自:***********@chatroom 发送人:wxid_rwfb9vuy93jn22 @:[] 内容:搞不出来
2025-07-30 17:44:31 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 17:44:31 | DEBUG | 处理消息内容: '搞不出来'
2025-07-30 17:44:31 | DEBUG | 消息内容 '搞不出来' 不匹配任何命令，忽略
2025-07-30 17:44:39 | DEBUG | 收到消息: {'MsgId': 1707990838, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n没推就没推呗，机器人又不是一直在线'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868678, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_RYiGcQcb|v1_9cIxuPDk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3180240986026006401, 'MsgSeq': 871412216}
2025-07-30 17:44:39 | INFO | 收到文本消息: 消息ID:1707990838 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:没推就没推呗，机器人又不是一直在线
2025-07-30 17:44:39 | DEBUG | 处理消息内容: '没推就没推呗，机器人又不是一直在线'
2025-07-30 17:44:39 | DEBUG | 消息内容 '没推就没推呗，机器人又不是一直在线' 不匹配任何命令，忽略
2025-07-30 17:44:42 | DEBUG | 收到消息: {'MsgId': 592866741, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14712338379154987085</objectId>\n\t\t\t<objectNonceId>11491889267533443724_6_25_12_3_1753868338771786_0492f8ce-6d29-11f0-aeeb-079dbd115e20</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>浦江小朱探房</nickname>\n\t\t\t<username>v2_060000231003b20faec8cae28e10cad6ce04ed33b077f34b7f36fd204bc314c34355e8bf519b@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/bnejMiaN6JKIuQCicVUeeYuib61UxLRGiawFA6o0ndOoKYpNRQfaERVVfAKru9P9Zh67P9LXAcwPaGq6f8fzhtZ3QFBCIdic3uSWJGoPot6flmfA/0]]></avatar>\n\t\t\t<desc>房价是怎么止跌回稳的#房价 #楼市 #楼市房产 #浦江镇二手房 #浦江小朱探房</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>1</authIconType>\n\t\t\t<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level2_588a474b89e94776b07b87dc361fd0ab.png]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQAaMwy2iciapbz08kvQnz5K68TGZuNp8lMqMwh6PvWeGQAhk5qnFGOMGYcIjONtPA9URv0h6LsjMoNSYGhAp375Tc&hy=SH&idx=1&m=&uzid=7a1ac&token=6xykWLEnztJD1V02HxcJfeoldDRKriavYWPbuia3hwUyUMmeVD7eZHhibsGbcpf7dFOpwVb9sU4c0lvVT4GGs1gwuwIDc2ENvVUeicqkCd1yJ7t97ChPicGLq6tCVKxNrttA05OOWqSvwYfLFycZkWu4WTfiaGNfOOdklkia5ffK8CYcw4&basedata=CAESBnhXVDEyMhoGeFdUMTExGgZ4V1QxMTIaBnhXVDExMxoGeFdUMTI2GgZ4V1QxMjcaBnhXVDEyMhoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwjoHBAAGAI&sign=FKRMVm_DC4ZxJupf5_5MKoeo4tmzSTbU00F5dSaQFArRzKgSZb_xMiUao-a7aKmLRL1wkuU-n_3aLrqdgplVug&ctsc=25&extg=10ab100&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAAAB2AysnDMAThcMNeiJaBAAAADnaHZTnGbFfAj9RgZXfw6VW4bMQn%2BkZ%2FDJZYx5c5s70RMFFIM0mbrKi9smnPu7cJwqHXqzJUdhlg%3D%3D&svrnonce=1753868341]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv5sOY9XwT43zia28IFZ6OLpIOVWfdwfpgN0Tkwa7wAAKIK2ExF01LLEkmwtjeLZEpGdWqzwJof38yevMic35OtB7e8Tt9muBMqGsoDW6WSrlRw&hy=SH&idx=1&m=de07873fb0768646e9226a7e29c46fcd&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE1mxtWxDcW1M3cfrkvPiaFPGdFdTWVaic9ZJhqkGwLyatMPjib3Bs0flPN0DWw01hI76Nn1q4fTcfU2dAVJ5BxSHrJCRQfojJo6JYNJP334WhVHQgFboQ8pT12IskricojViccib8wj5VAq35x&ctsc=2-25]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv5sOY9XwT43zia28IFZ6OLpIOVWfdwfpgN0Tkwa7wAAKIK2ExF01LLEkmwtjeLZEpGdWqzwJof38yevMic35OtB7e8Tt9muBMqGsoDW6WSrlRw&hy=SH&idx=1&m=de07873fb0768646e9226a7e29c46fcd&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE1mxtWxDcW1M3cfrkvPiaFPGdFdTWVaic9ZJhqkGwLyatMPjib3Bs0flPN0DWw01hI76Nn1q4fTcfU2dAVJ5BxSHrJCRQfojJo6JYNJP334WhVHQgFboQ8pT12IskricojViccib8wj5VAq35x&ctsc=2-25]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width>1080.0</width>\n\t\t\t\t\t<height>1920.0</height>\n\t\t\t\t\t<videoPlayDuration>46</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>25</sourceCommentScene>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868682, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>fb79a5d6fe18ac233a97e5d05427392a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_RD2XW+Zc|v1_95dlWbAO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 1417000602236086939, 'MsgSeq': 871412217}
2025-07-30 17:44:42 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-30 17:44:42 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14712338379154987085</objectId>
			<objectNonceId>11491889267533443724_6_25_12_3_1753868338771786_0492f8ce-6d29-11f0-aeeb-079dbd115e20</objectNonceId>
			<feedType>4</feedType>
			<nickname>浦江小朱探房</nickname>
			<username>v2_060000231003b20faec8cae28e10cad6ce04ed33b077f34b7f36fd204bc314c34355e8bf519b@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/bnejMiaN6JKIuQCicVUeeYuib61UxLRGiawFA6o0ndOoKYpNRQfaERVVfAKru9P9Zh67P9LXAcwPaGq6f8fzhtZ3QFBCIdic3uSWJGoPot6flmfA/0]]></avatar>
			<desc>房价是怎么止跌回稳的#房价 #楼市 #楼市房产 #浦江镇二手房 #浦江小朱探房</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>1</authIconType>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level2_588a474b89e94776b07b87dc361fd0ab.png]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQAaMwy2iciapbz08kvQnz5K68TGZuNp8lMqMwh6PvWeGQAhk5qnFGOMGYcIjONtPA9URv0h6LsjMoNSYGhAp375Tc&hy=SH&idx=1&m=&uzid=7a1ac&token=6xykWLEnztJD1V02HxcJfeoldDRKriavYWPbuia3hwUyUMmeVD7eZHhibsGbcpf7dFOpwVb9sU4c0lvVT4GGs1gwuwIDc2ENvVUeicqkCd1yJ7t97ChPicGLq6tCVKxNrttA05OOWqSvwYfLFycZkWu4WTfiaGNfOOdklkia5ffK8CYcw4&basedata=CAESBnhXVDEyMhoGeFdUMTExGgZ4V1QxMTIaBnhXVDExMxoGeFdUMTI2GgZ4V1QxMjcaBnhXVDEyMhoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwjoHBAAGAI&sign=FKRMVm_DC4ZxJupf5_5MKoeo4tmzSTbU00F5dSaQFArRzKgSZb_xMiUao-a7aKmLRL1wkuU-n_3aLrqdgplVug&ctsc=25&extg=10ab100&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAAAB2AysnDMAThcMNeiJaBAAAADnaHZTnGbFfAj9RgZXfw6VW4bMQn%2BkZ%2FDJZYx5c5s70RMFFIM0mbrKi9smnPu7cJwqHXqzJUdhlg%3D%3D&svrnonce=1753868341]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv5sOY9XwT43zia28IFZ6OLpIOVWfdwfpgN0Tkwa7wAAKIK2ExF01LLEkmwtjeLZEpGdWqzwJof38yevMic35OtB7e8Tt9muBMqGsoDW6WSrlRw&hy=SH&idx=1&m=de07873fb0768646e9226a7e29c46fcd&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE1mxtWxDcW1M3cfrkvPiaFPGdFdTWVaic9ZJhqkGwLyatMPjib3Bs0flPN0DWw01hI76Nn1q4fTcfU2dAVJ5BxSHrJCRQfojJo6JYNJP334WhVHQgFboQ8pT12IskricojViccib8wj5VAq35x&ctsc=2-25]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv5sOY9XwT43zia28IFZ6OLpIOVWfdwfpgN0Tkwa7wAAKIK2ExF01LLEkmwtjeLZEpGdWqzwJof38yevMic35OtB7e8Tt9muBMqGsoDW6WSrlRw&hy=SH&idx=1&m=de07873fb0768646e9226a7e29c46fcd&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE1mxtWxDcW1M3cfrkvPiaFPGdFdTWVaic9ZJhqkGwLyatMPjib3Bs0flPN0DWw01hI76Nn1q4fTcfU2dAVJ5BxSHrJCRQfojJo6JYNJP334WhVHQgFboQ8pT12IskricojViccib8wj5VAq35x&ctsc=2-25]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>46</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>25</sourceCommentScene>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 17:44:42 | DEBUG | XML消息类型: 51
2025-07-30 17:44:42 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-30 17:44:42 | DEBUG | XML消息描述: None
2025-07-30 17:44:42 | DEBUG | 附件信息 totallen: 0
2025-07-30 17:44:42 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-30 17:44:42 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-30 17:44:42 | INFO | 未知的XML消息类型: 51
2025-07-30 17:44:42 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-30 17:44:42 | INFO | 消息描述: None
2025-07-30 17:44:42 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-30 17:44:42 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14712338379154987085</objectId>
			<objectNonceId>11491889267533443724_6_25_12_3_1753868338771786_0492f8ce-6d29-11f0-aeeb-079dbd115e20</objectNonceId>
			<feedType>4</feedType>
			<nickname>浦江小朱探房</nickname>
			<username>v2_060000231003b20faec8cae28e10cad6ce04ed33b077f34b7f36fd204bc314c34355e8bf519b@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/bnejMiaN6JKIuQCicVUeeYuib61UxLRGiawFA6o0ndOoKYpNRQfaERVVfAKru9P9Zh67P9LXAcwPaGq6f8fzhtZ3QFBCIdic3uSWJGoPot6flmfA/0]]></avatar>
			<desc>房价是怎么止跌回稳的#房价 #楼市 #楼市房产 #浦江镇二手房 #浦江小朱探房</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>1</authIconType>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level2_588a474b89e94776b07b87dc361fd0ab.png]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQAaMwy2iciapbz08kvQnz5K68TGZuNp8lMqMwh6PvWeGQAhk5qnFGOMGYcIjONtPA9URv0h6LsjMoNSYGhAp375Tc&hy=SH&idx=1&m=&uzid=7a1ac&token=6xykWLEnztJD1V02HxcJfeoldDRKriavYWPbuia3hwUyUMmeVD7eZHhibsGbcpf7dFOpwVb9sU4c0lvVT4GGs1gwuwIDc2ENvVUeicqkCd1yJ7t97ChPicGLq6tCVKxNrttA05OOWqSvwYfLFycZkWu4WTfiaGNfOOdklkia5ffK8CYcw4&basedata=CAESBnhXVDEyMhoGeFdUMTExGgZ4V1QxMTIaBnhXVDExMxoGeFdUMTI2GgZ4V1QxMjcaBnhXVDEyMhoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwjoHBAAGAI&sign=FKRMVm_DC4ZxJupf5_5MKoeo4tmzSTbU00F5dSaQFArRzKgSZb_xMiUao-a7aKmLRL1wkuU-n_3aLrqdgplVug&ctsc=25&extg=10ab100&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAAAB2AysnDMAThcMNeiJaBAAAADnaHZTnGbFfAj9RgZXfw6VW4bMQn%2BkZ%2FDJZYx5c5s70RMFFIM0mbrKi9smnPu7cJwqHXqzJUdhlg%3D%3D&svrnonce=1753868341]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv5sOY9XwT43zia28IFZ6OLpIOVWfdwfpgN0Tkwa7wAAKIK2ExF01LLEkmwtjeLZEpGdWqzwJof38yevMic35OtB7e8Tt9muBMqGsoDW6WSrlRw&hy=SH&idx=1&m=de07873fb0768646e9226a7e29c46fcd&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE1mxtWxDcW1M3cfrkvPiaFPGdFdTWVaic9ZJhqkGwLyatMPjib3Bs0flPN0DWw01hI76Nn1q4fTcfU2dAVJ5BxSHrJCRQfojJo6JYNJP334WhVHQgFboQ8pT12IskricojViccib8wj5VAq35x&ctsc=2-25]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv5sOY9XwT43zia28IFZ6OLpIOVWfdwfpgN0Tkwa7wAAKIK2ExF01LLEkmwtjeLZEpGdWqzwJof38yevMic35OtB7e8Tt9muBMqGsoDW6WSrlRw&hy=SH&idx=1&m=de07873fb0768646e9226a7e29c46fcd&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE1mxtWxDcW1M3cfrkvPiaFPGdFdTWVaic9ZJhqkGwLyatMPjib3Bs0flPN0DWw01hI76Nn1q4fTcfU2dAVJ5BxSHrJCRQfojJo6JYNJP334WhVHQgFboQ8pT12IskricojViccib8wj5VAq35x&ctsc=2-25]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>46</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>25</sourceCommentScene>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 17:45:28 | DEBUG | 收到消息: {'MsgId': 1129010339, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_002lrj9uidgz22:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="6dc9949715d11d0ba0e0b2315474d8bd" cdnvideourl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e9b5042439326563663665342d333330372d343539302d623338332d35323662346431396139313902040d2408040201000405004c4e6100" cdnthumbaeskey="6dc9949715d11d0ba0e0b2315474d8bd" cdnthumburl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e9b5042439326563663665342d333330372d343539302d623338332d35323662346431396139313902040d2408040201000405004c4e6100" length="908404" playlength="19" cdnthumblength="24716" cdnthumbwidth="319" cdnthumbheight="512" fromusername="wxid_002lrj9uidgz22" md5="849bcfac12ffcb827d6f90fc467c9e03" newmd5="e70584c2fea201600c283f1da8d78b2a" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868727, 'MsgSource': '<msgsource>\n\t<videopreloadlen>261576</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>5cf1db32022a94b20c84c26ae19e7a0f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_SHtncw2A|v1_LoizDUQT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '7在群聊中发了一段视频', 'NewMsgId': *******************, 'MsgSeq': 871412218}
2025-07-30 17:45:28 | INFO | 收到视频消息: 消息ID:1129010339 来自:48097389945@chatroom 发送人:wxid_002lrj9uidgz22 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="6dc9949715d11d0ba0e0b2315474d8bd" cdnvideourl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e9b5042439326563663665342d333330372d343539302d623338332d35323662346431396139313902040d2408040201000405004c4e6100" cdnthumbaeskey="6dc9949715d11d0ba0e0b2315474d8bd" cdnthumburl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e9b5042439326563663665342d333330372d343539302d623338332d35323662346431396139313902040d2408040201000405004c4e6100" length="908404" playlength="19" cdnthumblength="24716" cdnthumbwidth="319" cdnthumbheight="512" fromusername="wxid_002lrj9uidgz22" md5="849bcfac12ffcb827d6f90fc467c9e03" newmd5="e70584c2fea201600c283f1da8d78b2a" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-07-30 17:45:37 | DEBUG | 收到消息: {'MsgId': 984365023, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n瑶瑶又罢工'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868736, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Zb9hk95i|v1_Hp2gFbii</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1849947106908127266, 'MsgSeq': 871412219}
2025-07-30 17:45:37 | INFO | 收到文本消息: 消息ID:984365023 来自:27852221909@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:瑶瑶又罢工
2025-07-30 17:45:37 | DEBUG | 处理消息内容: '瑶瑶又罢工'
2025-07-30 17:45:37 | DEBUG | 消息内容 '瑶瑶又罢工' 不匹配任何命令，忽略
2025-07-30 17:46:02 | INFO | 发送图片消息: 对方wxid:27852221909@chatroom 图片base64略
2025-07-30 17:46:02 | DEBUG | [TempFileManager] 已清理文件: temp\yaoyao\card_1753868757.png
2025-07-30 17:46:18 | DEBUG | 收到消息: {'MsgId': 1066637401, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'zuoledd:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这老傻逼，没有预判能力么</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>43</type>\n\t\t\t<svrid>*******************</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_002lrj9uidgz22</chatusr>\n\t\t\t<displayname>7</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;videomsg length="908404" playlength="19" offset="0" rawoffset="0" fromusername="wxid_002lrj9uidgz22" status="6" cameratype="0" source="1"                                              aeskey="6dc9949715d11d0ba0e0b2315474d8bd" cdnvideourl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e9b5042439326563663665342d333330372d343539302d623338332d35323662346431396139313902040d2408040201000405004c4e6100" cdnthumburl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e9b5042439326563663665342d333330372d343539302d623338332d35323662346431396139313902040d2408040201000405004c4e6100" cdnthumblength="24716" cdnthumbwidth="319" cdnthumbheight="512" cdnthumbaeskey="6dc9949715d11d0ba0e0b2315474d8bd" encryver="1" fileparam="" md5 ="849bcfac12ffcb827d6f90fc467c9e03" newmd5 ="e70584c2fea201600c283f1da8d78b2a" originsourcemd5 =""  filekey="48097389945@chatroom_35903_1753868727" uploadcontinuecount="0" rawlength="0" rawmd5="" cdnrawvideourl="" cdnrawvideoaeskey="" overwritemsgcreatetime="0" overwritenewmsgid="0" videouploadtoken="" isplaceholder="0" rawuploadcontinuecount="0"  videoFormat="2" rawVideoFormat="0" &gt;&lt;/videomsg&gt;&lt;statextstr&gt;&lt;/statextstr&gt;&lt;encodejson&gt;&lt;![CDATA[]]&gt;&lt;/encodejson&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;887882054&lt;/sequence_id&gt;\n\t&lt;videopreloadlen&gt;261576&lt;/videopreloadlen&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;5cf1db32022a94b20c84c26ae19e7a0f_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;videomsg_pd cdnvideourl_size="908404" cdnvideourl_score_all="1:10000;" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_xyzw14+T|v1_KZKBuqqf&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753868727</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>zuoledd</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868778, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>c9bd04f1e94d635a0461db70e47051f3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_AWGERcN0|v1_UZpXI+RY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '作乐多端 : 这老傻逼，没有预判能力么', 'NewMsgId': 4470976265181352694, 'MsgSeq': 871412222}
2025-07-30 17:46:18 | DEBUG | 从群聊消息中提取发送者: zuoledd
2025-07-30 17:46:18 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 17:46:18 | INFO | 收到引用消息: 消息ID:1066637401 来自:48097389945@chatroom 发送人:zuoledd 内容:这老傻逼，没有预判能力么 引用类型:43
2025-07-30 17:46:19 | INFO | [DouBaoImageToImage] 收到引用消息: 这老傻逼，没有预判能力么
2025-07-30 17:46:19 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 17:46:19 | INFO |   - 消息内容: 这老傻逼，没有预判能力么
2025-07-30 17:46:19 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-30 17:46:19 | INFO |   - 发送人: zuoledd
2025-07-30 17:46:19 | INFO |   - 引用信息: {'MsgType': 43, 'Content': '<msg><videomsg length="908404" playlength="19" offset="0" rawoffset="0" fromusername="wxid_002lrj9uidgz22" status="6" cameratype="0" source="1"                                              aeskey="6dc9949715d11d0ba0e0b2315474d8bd" cdnvideourl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e9b5042439326563663665342d333330372d343539302d623338332d35323662346431396139313902040d2408040201000405004c4e6100" cdnthumburl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e9b5042439326563663665342d333330372d343539302d623338332d35323662346431396139313902040d2408040201000405004c4e6100" cdnthumblength="24716" cdnthumbwidth="319" cdnthumbheight="512" cdnthumbaeskey="6dc9949715d11d0ba0e0b2315474d8bd" encryver="1" fileparam="" md5 ="849bcfac12ffcb827d6f90fc467c9e03" newmd5 ="e70584c2fea201600c283f1da8d78b2a" originsourcemd5 =""  filekey="48097389945@chatroom_35903_1753868727" uploadcontinuecount="0" rawlength="0" rawmd5="" cdnrawvideourl="" cdnrawvideoaeskey="" overwritemsgcreatetime="0" overwritenewmsgid="0" videouploadtoken="" isplaceholder="0" rawuploadcontinuecount="0"  videoFormat="2" rawVideoFormat="0" ></videomsg><statextstr></statextstr><encodejson><![CDATA[]]></encodejson></msg>', 'Msgid': '*******************', 'NewMsgId': '*******************', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '7', 'MsgSource': '<msgsource><sequence_id>887882054</sequence_id>\n\t<videopreloadlen>261576</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>5cf1db32022a94b20c84c26ae19e7a0f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<videomsg_pd cdnvideourl_size="908404" cdnvideourl_score_all="1:10000;" />\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_xyzw14+T|v1_KZKBuqqf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753868727', 'SenderWxid': 'zuoledd'}
2025-07-30 17:46:19 | INFO |   - 引用消息ID: 
2025-07-30 17:46:19 | INFO |   - 引用消息类型: 
2025-07-30 17:46:19 | INFO |   - 引用消息内容: <msg><videomsg length="908404" playlength="19" offset="0" rawoffset="0" fromusername="wxid_002lrj9uidgz22" status="6" cameratype="0" source="1"                                              aeskey="6dc9949715d11d0ba0e0b2315474d8bd" cdnvideourl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e9b5042439326563663665342d333330372d343539302d623338332d35323662346431396139313902040d2408040201000405004c4e6100" cdnthumburl="3057020100044b3049020100020472948eb302032f514902049f328e7102046889e9b5042439326563663665342d333330372d343539302d623338332d35323662346431396139313902040d2408040201000405004c4e6100" cdnthumblength="24716" cdnthumbwidth="319" cdnthumbheight="512" cdnthumbaeskey="6dc9949715d11d0ba0e0b2315474d8bd" encryver="1" fileparam="" md5 ="849bcfac12ffcb827d6f90fc467c9e03" newmd5 ="e70584c2fea201600c283f1da8d78b2a" originsourcemd5 =""  filekey="48097389945@chatroom_35903_1753868727" uploadcontinuecount="0" rawlength="0" rawmd5="" cdnrawvideourl="" cdnrawvideoaeskey="" overwritemsgcreatetime="0" overwritenewmsgid="0" videouploadtoken="" isplaceholder="0" rawuploadcontinuecount="0"  videoFormat="2" rawVideoFormat="0" ></videomsg><statextstr></statextstr><encodejson><![CDATA[]]></encodejson></msg>
2025-07-30 17:46:19 | INFO |   - 引用消息发送人: zuoledd
2025-07-30 17:46:25 | DEBUG | 收到消息: {'MsgId': 445457238, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868784, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_8HGD+pB/|v1_e8Cbf2Mr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女', 'NewMsgId': 5629607751009868356, 'MsgSeq': 871412223}
2025-07-30 17:46:25 | INFO | 收到文本消息: 消息ID:445457238 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女
2025-07-30 17:46:25 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女 from wxid_ubbh6q832tcs21
2025-07-30 17:46:25 | DEBUG | [DoubaoVideoSearch] 开始处理视频搜索请求，用户: wxid_ubbh6q832tcs21
2025-07-30 17:46:25 | DEBUG | [DoubaoVideoSearch] 用户限制检查 - 用户: wxid_ubbh6q832tcs21, 上次请求: 1753868785.53秒前, 等待时间: 0.00秒
2025-07-30 17:46:25 | DEBUG | [DoubaoVideoSearch] 令牌桶状态: 5.00/5
2025-07-30 17:46:25 | DEBUG | [DoubaoVideoSearch] 成功获取令牌，剩余: 4.00
2025-07-30 17:46:25 | INFO | [DoubaoVideoSearch] 开始搜索视频，关键词: '美女'
2025-07-30 17:46:25 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女
2025-07-30 17:46:25 | DEBUG | [DoubaoVideoSearch] 调试信息 - 设备ID: 7468716989062841895, Web ID: 7468716986638386703
2025-07-30 17:46:25 | DEBUG | [DoubaoVideoSearch] 调试信息 - Cookies长度: 2107
2025-07-30 17:46:25 | DEBUG | [DoubaoVideoSearch] 第1次尝试，开始构造请求参数
2025-07-30 17:46:25 | DEBUG | [DoubaoVideoSearch] 生成会话ID: 38175386878553658, 消息ID: ba0d6520-b572-11f0-a188-0d4deee36231
2025-07-30 17:46:25 | DEBUG | [DoubaoVideoSearch] 构造搜索查询: 搜索抖音视频:美女
2025-07-30 17:46:25 | DEBUG | [DoubaoVideoSearch] 请求URL: https://www.doubao.com/samantha/chat/completion?aid=497858&device_id=7468716989062841895&device_plat...
2025-07-30 17:46:25 | DEBUG | [DoubaoVideoSearch] 请求数据大小: 537 字符
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 收到响应，状态码: 200
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 响应头: {'server': 'Tengine', 'content-type': 'text/event-stream', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'date': 'Wed, 30 Jul 2025 09:46:26 GMT', 'cache-control': 'no-cache', 'x-tt-agw-login': '1', 'x-tt-logid': '20250730174625C9CD2747DDB90AFADFBB', 'server-timing': 'inner; dur=149,tt_agw; dur=137', 'x-ms-token': 'BzYq5GHF4q0sMWQ2EdDKIeoVL1W65hlXbQ4NDBAPU22f9u3gwJnxG7zpqe0pMmdHGkDguI2cygGtFDkz7RQs20GDyk1wDx_m6r10IHq52o50r96jRL-R_QtAB321z-g=', 'x-envoy-response-flags': '-', 'x-tt-trace-host': '01a904f32dfda387cbf0c81d4c4d78f48a57c7d8f6d38a8c14808bec1094876f848ef19316ce4ed470b905445ab4e8862f935e79bb5f285f696c82aa380c7c402dabecbea1bae0b094f26fa4fe99f2393cf07347f64558fef2907234a603a0abd4c5ca0b4baa0cd8b8ec42fe62a7c9dbe5', 'x-tt-trace-tag': 'id=03;cdn-cache=miss;type=dyn', 'x-tt-trace-id': '00-250730174625C9CD2747DDB90AFADFBB-70A806A973D7432E-00', 'x-tt-timestamp': '1753868786.070', 'via': 'ens-cache24.cn7025[191,0]', 'timing-allow-origin': '*', 'eagleid': 'db999d2c17538687859036395e'}
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 响应内容长度: 1268 字节
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 开始处理响应流数据
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 开始处理SSE响应流
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 响应数据类型: <class 'bytes'>, 大小: 1268 字节
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 解码后数据长度: 1260 字符
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 处理第 1 个事件，数据长度: 1192
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 事件类型: 2005
2025-07-30 17:46:26 | WARNING | [DoubaoVideoSearch] 收到错误事件: {'code': 710022004, 'message': 'rate limited', 'error_detail': {'code': 710022004, 'locale': 'zh', 'message': '系统错误', 'ext': {'decision': '{"code":"10000","from":"shark_admin","version":"","type":"verify","region":"cn","subtype":"semantic_reasoning","detail":"YMsl50aucIXrXz222-2PIxgJ6oTlqhbPCsYduwwRA7txl*mPKFvP*x42NYM3SGer-2fA8naMefsEF0xFd86BpXbdenwZ52RdK6GvYJn75w*OE3amXrLBur1t2Ad*aXFm9ubx3TAsUuitMKYJXyEHouAdcbafdW1UyijWmug2FyxYCmxeVNFyN-Ten4l*D-YzeonhD44sq1dPpG8jxwU9Hih1VNUICZWLjB1JzQC54I0-l7JzYpyMAcFc92TS6UkYTsyzRjqEPnTKri0syxZyckGN2ANJ10hvYcawAxkA2MhwCyVf2wilHqMLT03smxOmv*7QvkDJy-HFeGTldRsqTBmLcjkyhR5YR1FuMLxs93wIy2HXl78kiKcc-A8cIi2xKHy2L*0ghUxVcvJvGl6qajJtd7sUK3ivirlDafkEcgF79aN8Xou-bSFzWiQWt9e8FfgpTnVGmzBKblkHKIFMr0qbij*NkeUpn2J8N5byiRu04I529Ml4i4I2kZyu","server_sdk_env":"{\\"idc\\":\\"hl\\",\\"region\\":\\"CN\\",\\"server_type\\":\\"business\\"}","log_id":"20250730174625C9CD2747DDB90AFADFBB","verify_scene":"doubao_message_web"}'}}}
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 处理第 2 个事件，数据长度: 52
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 事件类型: 2003
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 收到结束事件
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 结束事件：返回文本结果
2025-07-30 17:46:26 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: text
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 开始处理搜索结果
2025-07-30 17:46:26 | DEBUG | [DoubaoVideoSearch] 开始处理视频消息，结果类型: text
2025-07-30 17:46:26 | WARNING | [DoubaoVideoSearch] 结果类型不是视频: text
2025-07-30 17:46:26 | INFO | 发送文字消息: 对方wxid:***********@chatroom at: 内容:弄不了
2025-07-30 17:46:26 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 0.85秒
2025-07-30 17:46:26 | DEBUG | 处理消息内容: '找视频 美女'
2025-07-30 17:46:26 | DEBUG | 消息内容 '找视频 美女' 不匹配任何命令，忽略
2025-07-30 17:47:51 | DEBUG | 收到消息: {'MsgId': 1012945329, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_002lrj9uidgz22:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="27a17379142895c5664f69e2e2d111c4" cdnvideourl="3057020100044b30490201000204e9aa838a02032f5aa90204999969710204688587c0042433326565636232662d356639332d346532652d623435322d6462313931316263646631360204052408040201000405004c51e500" cdnthumbaeskey="27a17379142895c5664f69e2e2d111c4" cdnthumburl="3057020100044b30490201000204e9aa838a02032f5aa90204999969710204688587c0042433326565636232662d356639332d346532652d623435322d6462313931316263646631360204052408040201000405004c51e500" length="1522254" playlength="19" cdnthumblength="16845" cdnthumbwidth="512" cdnthumbheight="288" fromusername="wxid_002lrj9uidgz22" md5="8bef24b85f80c0ce7a11e09b168d0dcc" newmd5="763e827fd2a0ff346155bc5a1158f83c" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="8bef24b85f80c0ce7a11e09b168d0dcc" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868871, 'MsgSource': '<msgsource>\n\t<videopreloadlen>421893</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>aaebbf3496590771d92850e7f7790e35_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_giBz4Aam|v1_NWdymRTj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '7在群聊中发了一段视频', 'NewMsgId': 8319679539655154761, 'MsgSeq': 871412226}
2025-07-30 17:47:51 | INFO | 收到视频消息: 消息ID:1012945329 来自:48097389945@chatroom 发送人:wxid_002lrj9uidgz22 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="27a17379142895c5664f69e2e2d111c4" cdnvideourl="3057020100044b30490201000204e9aa838a02032f5aa90204999969710204688587c0042433326565636232662d356639332d346532652d623435322d6462313931316263646631360204052408040201000405004c51e500" cdnthumbaeskey="27a17379142895c5664f69e2e2d111c4" cdnthumburl="3057020100044b30490201000204e9aa838a02032f5aa90204999969710204688587c0042433326565636232662d356639332d346532652d623435322d6462313931316263646631360204052408040201000405004c51e500" length="1522254" playlength="19" cdnthumblength="16845" cdnthumbwidth="512" cdnthumbheight="288" fromusername="wxid_002lrj9uidgz22" md5="8bef24b85f80c0ce7a11e09b168d0dcc" newmd5="763e827fd2a0ff346155bc5a1158f83c" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="8bef24b85f80c0ce7a11e09b168d0dcc" isad="0" />
</msg>

2025-07-30 17:48:11 | DEBUG | 收到消息: {'MsgId': 202288939, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_laurnst5xn0q22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>老登是这样的</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>4470976265181352694</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>zuoledd</chatusr>\n\t\t\t<displayname>作乐多端</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;c9bd04f1e94d635a0461db70e47051f3_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_esa6xjHs|v1_r2UG0rZF&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;appmsg appid="" sdkver="0"&gt;\n\t\t&lt;title&gt;这老傻逼，没有预判能力么&lt;/title&gt;\n\t\t&lt;type&gt;57&lt;/type&gt;\n\t\t&lt;appattach&gt;\n\t\t\t&lt;cdnthumbaeskey /&gt;\n\t\t\t&lt;aeskey&gt;&lt;/aeskey&gt;\n\t\t&lt;/appattach&gt;\n\t\t&lt;refermsg&gt;&lt;/refermsg&gt;\n\t&lt;/appmsg&gt;\n\t&lt;fromusername&gt;zuoledd&lt;/fromusername&gt;\n\t&lt;scene&gt;0&lt;/scene&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;version&gt;1&lt;/version&gt;\n\t\t&lt;appname /&gt;\n\t&lt;/appinfo&gt;\n\t&lt;commenturl /&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753868778</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t\t<listenItem>null</listenItem>\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_laurnst5xn0q22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753868891, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>c9bd04f1e94d635a0461db70e47051f3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_3DzW8EQ4|v1_2Ku1Lw2l</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : 老登是这样的', 'NewMsgId': 2785903491131522982, 'MsgSeq': 871412227}
2025-07-30 17:48:11 | DEBUG | 从群聊消息中提取发送者: wxid_laurnst5xn0q22
2025-07-30 17:48:11 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 17:48:11 | INFO | 收到引用消息: 消息ID:202288939 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 内容:老登是这样的 引用类型:49
2025-07-30 17:48:12 | INFO | [DouBaoImageToImage] 收到引用消息: 老登是这样的
2025-07-30 17:48:12 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 17:48:12 | INFO |   - 消息内容: 老登是这样的
2025-07-30 17:48:12 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-30 17:48:12 | INFO |   - 发送人: wxid_laurnst5xn0q22
2025-07-30 17:48:12 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这老傻逼，没有预判能力么</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg></refermsg>\n\t</appmsg>\n\t<fromusername>zuoledd</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n', 'Msgid': '4470976265181352694', 'NewMsgId': '4470976265181352694', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '作乐多端', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>c9bd04f1e94d635a0461db70e47051f3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_esa6xjHs|v1_r2UG0rZF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753868778', 'SenderWxid': 'wxid_laurnst5xn0q22'}
2025-07-30 17:48:12 | INFO |   - 引用消息ID: 
2025-07-30 17:48:12 | INFO |   - 引用消息类型: 
2025-07-30 17:48:12 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>这老傻逼，没有预判能力么</title>
		<type>57</type>
		<appattach>
			<cdnthumbaeskey />
			<aeskey></aeskey>
		</appattach>
		<refermsg></refermsg>
	</appmsg>
	<fromusername>zuoledd</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 17:48:12 | INFO |   - 引用消息发送人: wxid_laurnst5xn0q22
2025-07-30 17:50:39 | DEBUG | 收到消息: {'MsgId': 956534768, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_rwfb9vuy93jn22:\n弄不了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753869039, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_ZsM4D7vB|v1_M45laLbL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '黹忙 : 弄不了', 'NewMsgId': 2920769017163422727, 'MsgSeq': 871412228}
2025-07-30 17:50:39 | INFO | 收到文本消息: 消息ID:956534768 来自:***********@chatroom 发送人:wxid_rwfb9vuy93jn22 @:[] 内容:弄不了
2025-07-30 17:50:40 | DEBUG | 处理消息内容: '弄不了'
2025-07-30 17:50:40 | DEBUG | 消息内容 '弄不了' 不匹配任何命令，忽略
